name: Dependency Update Check

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch: # Allow manual trigger

jobs:
  check-dependencies:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
        
    - name: Install system dependencies for WeasyPrint
      run: |
        sudo apt-get update
        sudo apt-get install -y \
          libcairo2-dev \
          libpango1.0-dev \
          libgdk-pixbuf2.0-dev \
          libffi-dev \
          shared-mime-info
          
    - name: Create virtual environment
      run: |
        python -m venv venv
        source venv/bin/activate
        pip install --upgrade pip setuptools wheel
        
    - name: Install current dependencies
      run: |
        source venv/bin/activate
        cd backend
        pip install -r requirements.txt
        pip install -r requirements-weasyprint.txt
        pip install -r requirements-test.txt
        
    - name: Check for outdated dependencies
      run: |
        source venv/bin/activate
        cd backend
        python update_dependencies.py --check-only
        
    - name: Create issue if dependencies are outdated
      if: failure()
      uses: actions/github-script@v7
      with:
        script: |
          const title = '🔄 Dependencies Update Available';
          const body = `
          ## Outdated Dependencies Detected
          
          The weekly dependency check has found outdated packages in the CVFlo backend.
          
          ### Action Required
          1. Run \`cd backend && python update_dependencies.py\` locally
          2. Test the updated dependencies
          3. Commit and push the changes
          
          ### Or use the Makefile
          \`\`\`bash
          cd backend
          make update-deps
          make test
          \`\`\`
          
          This issue was automatically created by the dependency update workflow.
          `;
          
          // Check if issue already exists
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open',
            labels: ['dependencies']
          });
          
          const existingIssue = issues.data.find(issue => issue.title === title);
          
          if (!existingIssue) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['dependencies', 'maintenance']
            });
          }
