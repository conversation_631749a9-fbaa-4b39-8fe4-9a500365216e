#!/usr/bin/env python3
"""
Check Available Package Versions

This script checks PyPI for the actual latest versions of packages
to avoid version conflicts in requirements files.
"""

import subprocess
import sys
import json
import requests
from packaging import version


def get_package_info(package_name):
    """Get package information from PyPI"""
    try:
        response = requests.get(f"https://pypi.org/pypi/{package_name}/json", timeout=10)
        if response.status_code == 200:
            data = response.json()
            latest_version = data['info']['version']
            all_versions = list(data['releases'].keys())
            # Filter out pre-releases and sort
            stable_versions = [v for v in all_versions if not any(pre in v.lower() for pre in ['a', 'b', 'rc', 'dev'])]
            stable_versions.sort(key=lambda x: version.parse(x), reverse=True)
            return {
                'latest': latest_version,
                'stable_versions': stable_versions[:5],  # Top 5 stable versions
                'exists': True
            }
    except Exception as e:
        print(f"Error checking {package_name}: {e}")
    
    return {'exists': False}


def check_requirements_file(file_path):
    """Check versions in a requirements file"""
    print(f"\n📋 Checking {file_path}")
    print("=" * 50)
    
    try:
        with open(file_path, 'r') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if line and not line.startswith('#'):
            # Parse package name and version
            if '>=' in line or '==' in line:
                parts = line.split('>=') if '>=' in line else line.split('==')
                if len(parts) == 2:
                    package_name = parts[0].strip()
                    required_version = parts[1].strip().split(',')[0]  # Handle version ranges
                    
                    print(f"🔍 {package_name} (required: {required_version})")
                    
                    info = get_package_info(package_name)
                    if info['exists']:
                        latest = info['latest']
                        if version.parse(required_version) > version.parse(latest):
                            print(f"   ❌ Required version {required_version} > latest {latest}")
                        elif version.parse(required_version) == version.parse(latest):
                            print(f"   ✅ Up to date ({latest})")
                        else:
                            print(f"   📈 Can update: {required_version} → {latest}")
                        
                        # Show recent stable versions
                        if len(info['stable_versions']) > 1:
                            recent = ', '.join(info['stable_versions'][:3])
                            print(f"   📦 Recent versions: {recent}")
                    else:
                        print(f"   ❓ Could not check package")
                    print()


def main():
    """Main function"""
    print("🔍 CVFlo Package Version Checker")
    print("=" * 40)
    
    requirements_files = [
        'requirements.txt',
        'requirements-test.txt', 
        'requirements-weasyprint.txt'
    ]
    
    for req_file in requirements_files:
        check_requirements_file(req_file)
    
    print("\n💡 Tips:")
    print("- Use '>=' for flexibility while ensuring minimum versions")
    print("- Use version ranges like '>=6.1.0,<7.0.0' to avoid conflicts")
    print("- Check package changelogs before major version updates")
    print("- Test thoroughly after updating dependencies")


if __name__ == "__main__":
    main()
