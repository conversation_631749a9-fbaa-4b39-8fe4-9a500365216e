#!/usr/bin/env python3
"""
Supabase Connection Test Script

This script tests the connection to Supabase PostgreSQL database and authentication services.
Run this script to verify your Supabase integration is working correctly.
"""

import os
import sys
import json
import uuid
from pathlib import Path

# Add Django project to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cvflo.settings')

import django
django.setup()

from django.conf import settings
from django.db import connection, transaction
from django.contrib.auth.models import User
from apps.authentication.backends import SupabaseService
from apps.cv_builder.models import CVProfile, UserProfile, PDFGenerationLog


class SupabaseConnectionTester:
    """Comprehensive Supabase connection and integration tester"""
    
    def __init__(self):
        self.results = {
            'database': False,
            'authentication': False,
            'models': False,
            'operations': False,
            'errors': []
        }
        
    def test_database_connection(self):
        """Test PostgreSQL database connection"""
        print("\n🔌 Testing Database Connection...")
        
        try:
            with connection.cursor() as cursor:
                # Test basic connection
                cursor.execute("SELECT version();")
                version = cursor.fetchone()[0]
                print(f"✅ Database connected successfully")
                print(f"   PostgreSQL Version: {version[:50]}...")
                
                # Test database name and user
                cursor.execute("SELECT current_database(), current_user;")
                db_name, db_user = cursor.fetchone()
                print(f"   Database: {db_name}")
                print(f"   User: {db_user}")
                
                # Test SSL connection
                cursor.execute("SELECT ssl_is_used();")
                ssl_used = cursor.fetchone()[0]
                print(f"   SSL Enabled: {ssl_used}")
                
                self.results['database'] = True
                return True
                
        except Exception as e:
            error_msg = f"Database connection failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.results['errors'].append(error_msg)
            return False
    
    def test_supabase_authentication(self):
        """Test Supabase authentication service"""
        print("\n🔐 Testing Supabase Authentication...")
        
        try:
            # Check configuration
            required_configs = ['SUPABASE_URL', 'SUPABASE_ANON_KEY']
            for config in required_configs:
                value = getattr(settings, config, '')
                if not value:
                    raise ValueError(f"{config} is not configured")
                print(f"   {config}: {'*' * (len(value) - 10)}{value[-10:]}")
            
            # Test service initialization
            service = SupabaseService()
            print("✅ Supabase service initialized successfully")
            
            # Test token format validation
            valid_token = service.validate_token_format("header.payload.signature")
            invalid_token = service.validate_token_format("invalid-token")
            
            if valid_token and not invalid_token:
                print("✅ Token format validation working")
            else:
                raise ValueError("Token format validation failed")
            
            self.results['authentication'] = True
            return True
            
        except Exception as e:
            error_msg = f"Authentication test failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.results['errors'].append(error_msg)
            return False
    
    def test_model_operations(self):
        """Test Django model operations with Supabase schema"""
        print("\n📊 Testing Model Operations...")
        
        try:
            test_user_id = uuid.uuid4()
            test_email = f"test-{uuid.uuid4().hex[:8]}@example.com"
            
            # Test Django User creation
            django_user = User.objects.create_user(
                username=str(test_user_id),
                email=test_email,
                first_name='Test',
                last_name='User'
            )
            print(f"✅ Django User created: {django_user.email}")
            
            # Test UserProfile creation
            user_profile = UserProfile.objects.create(
                id=test_user_id,
                email=test_email,
                full_name='Test User',
                preferences={'theme': 'light'}
            )
            print(f"✅ UserProfile created: {user_profile.id}")
            
            # Test CVProfile creation
            cv_profile = CVProfile.objects.create(
                user_id=test_user_id,
                template_name='classic-0',
                cv_content={
                    'personalInfo': {
                        'firstName': 'Test',
                        'lastName': 'User',
                        'email': test_email
                    },
                    'visibility': {
                        'personalInfo': True
                    }
                }
            )
            print(f"✅ CVProfile created: {cv_profile.id}")
            
            # Test PDFGenerationLog creation
            pdf_log = PDFGenerationLog.objects.create(
                user_id=test_user_id,
                cv_profile=cv_profile,
                template_name='classic-0',
                status='pending',
                priority='normal'
            )
            print(f"✅ PDFGenerationLog created: {pdf_log.id}")
            
            # Test relationships
            related_pdfs = cv_profile.pdf_generations.all()
            if related_pdfs.count() == 1:
                print("✅ Model relationships working")
            else:
                raise ValueError("Model relationships failed")
            
            # Cleanup test data
            django_user.delete()
            user_profile.delete()  # CV profile will cascade delete
            
            print("✅ Test data cleaned up")
            self.results['models'] = True
            return True
            
        except Exception as e:
            error_msg = f"Model operations failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.results['errors'].append(error_msg)
            return False
    
    def test_advanced_operations(self):
        """Test advanced database operations"""
        print("\n⚡ Testing Advanced Operations...")
        
        try:
            # Test transaction handling
            with transaction.atomic():
                test_user_id = uuid.uuid4()
                
                # Create multiple related objects in transaction
                user_profile = UserProfile.objects.create(
                    id=test_user_id,
                    email=f"advanced-test-{uuid.uuid4().hex[:8]}@example.com"
                )
                
                cv_profile = CVProfile.objects.create(
                    user_id=test_user_id,
                    template_name='modern-0',
                    cv_content={'test': 'data'}
                )
                
                # Test JSON field operations
                cv_profile.cv_content['personalInfo'] = {
                    'firstName': 'Advanced',
                    'lastName': 'Test'
                }
                cv_profile.save()
                
                # Verify JSON update
                updated_profile = CVProfile.objects.get(id=cv_profile.id)
                if updated_profile.cv_content['personalInfo']['firstName'] == 'Advanced':
                    print("✅ JSON field operations working")
                else:
                    raise ValueError("JSON field update failed")
                
                # Test UUID querying
                found_profile = CVProfile.objects.filter(user_id=test_user_id).first()
                if found_profile:
                    print("✅ UUID field querying working")
                else:
                    raise ValueError("UUID querying failed")
                
                # Cleanup within transaction
                cv_profile.delete()
                user_profile.delete()
            
            # Test connection pooling info
            with connection.cursor() as cursor:
                cursor.execute("SELECT count(*) FROM pg_stat_activity WHERE datname = current_database();")
                active_connections = cursor.fetchone()[0]
                print(f"✅ Active database connections: {active_connections}")
            
            self.results['operations'] = True
            return True
            
        except Exception as e:
            error_msg = f"Advanced operations failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.results['errors'].append(error_msg)
            return False
    
    def test_table_existence(self):
        """Test that required tables exist in database"""
        print("\n📋 Testing Table Existence...")
        
        required_tables = [
            'cv_data',
            'pdf_generations', 
            'user_profiles',
            'rate_limit_logs'
        ]
        
        try:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_type = 'BASE TABLE';
                """)
                existing_tables = [row[0] for row in cursor.fetchall()]
                
                missing_tables = []
                for table in required_tables:
                    if table in existing_tables:
                        print(f"✅ Table exists: {table}")
                    else:
                        missing_tables.append(table)
                        print(f"⚠️  Table missing: {table}")
                
                if missing_tables:
                    print(f"\n⚠️  Missing tables: {missing_tables}")
                    print("   Run 'python manage.py migrate' to create missing tables")
                    return False
                else:
                    print("✅ All required tables exist")
                    return True
                    
        except Exception as e:
            error_msg = f"Table existence check failed: {str(e)}"
            print(f"❌ {error_msg}")
            self.results['errors'].append(error_msg)
            return False
    
    def run_all_tests(self):
        """Run all tests and generate report"""
        print("🚀 Starting Supabase Integration Tests...")
        print(f"   Django Settings Module: {settings.SETTINGS_MODULE}")
        print(f"   Database Engine: {getattr(settings, 'DATABASE_ENGINE', 'not set')}")
        print(f"   Debug Mode: {settings.DEBUG}")
        
        # Run tests
        tests = [
            self.test_database_connection,
            self.test_supabase_authentication,
            self.test_table_existence,
            self.test_model_operations,
            self.test_advanced_operations
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
        
        # Generate report
        print("\n" + "="*60)
        print("📊 TEST RESULTS SUMMARY")
        print("="*60)
        print(f"Tests Passed: {passed}/{total}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if self.results['errors']:
            print("\n❌ ERRORS ENCOUNTERED:")
            for i, error in enumerate(self.results['errors'], 1):
                print(f"   {i}. {error}")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! Supabase integration is working correctly.")
            return True
        else:
            print(f"\n⚠️  {total - passed} test(s) failed. Please review the errors above.")
            return False
    
    def generate_config_template(self):
        """Generate configuration template"""
        print("\n📝 SUPABASE CONFIGURATION TEMPLATE")
        print("="*60)
        print("Add these variables to your .env file:")
        print()
        print("# Supabase Database Configuration")
        print("DATABASE_ENGINE=supabase")
        print("SUPABASE_DB_NAME=postgres")
        print("SUPABASE_DB_USER=postgres.your-project-id")
        print("SUPABASE_DB_PASSWORD=your-database-password")
        print("SUPABASE_DB_HOST=aws-0-us-east-1.pooler.supabase.com")
        print("SUPABASE_DB_PORT=6543")
        print("SUPABASE_DB_SSL_MODE=require")
        print()
        print("# Supabase Authentication")
        print("SUPABASE_URL=https://your-project.supabase.co")
        print("SUPABASE_ANON_KEY=your-anon-key")
        print("SUPABASE_SERVICE_ROLE_KEY=your-service-role-key")
        print()
        print("Get these values from your Supabase dashboard:")
        print("1. Go to https://supabase.com/dashboard")
        print("2. Select your project")
        print("3. Go to Settings > Database")
        print("4. Copy the connection details")
        print("5. Go to Settings > API")
        print("6. Copy the URL and keys")


def main():
    """Main function"""
    tester = SupabaseConnectionTester()
    
    # Check if basic configuration exists
    if not hasattr(settings, 'SUPABASE_URL') or not settings.SUPABASE_URL:
        print("⚠️  Supabase not configured. Generating configuration template...")
        tester.generate_config_template()
        return False
    
    # Run tests
    success = tester.run_all_tests()
    
    if not success:
        print("\n💡 TROUBLESHOOTING TIPS:")
        print("1. Verify your .env file has all required Supabase variables")
        print("2. Check that your Supabase project is active")
        print("3. Ensure your database password is correct")
        print("4. Run 'python manage.py migrate' to create tables")
        print("5. Check your network connection to Supabase")
        
        tester.generate_config_template()
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)