#!/usr/bin/env python3
"""
Script to get a valid user access token for testing authentication
"""

import os
import sys
import django

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cvflo.settings')
django.setup()

from supabase import create_client
from django.conf import settings

def get_user_token():
    """Get a user access token by signing in with email/password"""
    
    # Initialize Supabase client
    supabase = create_client(settings.SUPABASE_URL, settings.SUPABASE_ANON_KEY)
    
    print("🔐 CVFlo User Token Generator")
    print("=" * 40)
    
    # Get user credentials
    email = input("Enter your email: ").strip()
    password = input("Enter your password: ").strip()
    
    if not email or not password:
        print("❌ Email and password are required")
        return
    
    try:
        # Sign in with Supabase
        print("\n🔄 Signing in...")
        response = supabase.auth.sign_in_with_password({
            "email": email,
            "password": password
        })
        
        if response.user and response.session:
            print("✅ Sign in successful!")
            print(f"\n📧 User: {response.user.email}")
            print(f"🆔 User ID: {response.user.id}")
            print(f"\n🎫 Access Token:")
            print(f"{response.session.access_token}")
            
            print(f"\n🧪 Test command:")
            print(f"curl -X POST http://localhost:8000/api/pdf/generate-preview/ \\")
            print(f"  -H 'Content-Type: application/json' \\")
            print(f"  -H 'Authorization: Bearer {response.session.access_token}' \\")
            print(f"  -d '{{\"cv_data\": {{\"personalInfo\": {{\"firstName\": \"Test\", \"lastName\": \"User\"}}}}, \"visibility\": {{\"personalInfo\": true}}, \"template_name\": \"classic-0\"}}'")
            
        else:
            print("❌ Sign in failed - no user or session returned")
            
    except Exception as e:
        print(f"❌ Sign in failed: {str(e)}")
        print("\nPossible issues:")
        print("- Invalid email/password")
        print("- Email not confirmed")
        print("- Account doesn't exist")

if __name__ == "__main__":
    get_user_token()
