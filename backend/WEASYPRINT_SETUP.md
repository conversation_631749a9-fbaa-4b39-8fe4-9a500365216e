# WeasyPrint PDF Generation Setup Guide

This guide provides comprehensive instructions for setting up the production-ready WeasyPrint PDF generation system for the CVFlo Django + Supabase application.

## Overview

The WeasyPrint PDF generation system includes:

- **WeasyPrint Service**: Core PDF generation with memory optimization
- **Async Processing**: Celery + Redis for background PDF generation
- **Template System**: Flexible CV template management with print CSS
- **Storage Integration**: Secure file storage with Supabase Storage
- **Docker Support**: Production-ready containerization
- **Monitoring**: Health checks and performance tracking

## Prerequisites

### System Dependencies

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install -y \
    libcairo2-dev \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    libxml2-dev \
    libxslt-dev \
    fontconfig \
    fonts-dejavu-core \
    fonts-liberation \
    fonts-noto \
    gcc \
    g++ \
    pkg-config
```

#### macOS
```bash
brew install cairo pango gdk-pixbuf libffi libxml2 libxslt
```

#### CentOS/RHEL/Fedora
```bash
sudo yum install -y \
    cairo-devel \
    pango-devel \
    gdk-pixbuf2-devel \
    libffi-devel \
    libxml2-devel \
    libxslt-devel \
    fontconfig \
    dejavu-sans-fonts \
    liberation-fonts \
    gcc \
    gcc-c++ \
    pkgconfig
```

### Python Dependencies

Install the enhanced requirements:

```bash
pip install -r requirements.txt
pip install -r requirements-weasyprint.txt
```

### Redis Setup

For local development:
```bash
# Ubuntu/Debian
sudo apt-get install redis-server
sudo systemctl start redis-server

# macOS
brew install redis
brew services start redis

# Docker
docker run -d --name redis -p 6379:6379 redis:7-alpine
```

## Configuration

### Django Settings

Add to your `settings.py`:

```python
# Celery Configuration
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

# WeasyPrint Configuration
WEASYPRINT_MAX_WORKERS = int(os.environ.get('WEASYPRINT_MAX_WORKERS', 3))
WEASYPRINT_TIMEOUT = int(os.environ.get('WEASYPRINT_TIMEOUT', 60))
WEASYPRINT_MAX_MEMORY_MB = int(os.environ.get('WEASYPRINT_MAX_MEMORY_MB', 512))
WEASYPRINT_CACHE_TIMEOUT = int(os.environ.get('WEASYPRINT_CACHE_TIMEOUT', 300))

# PDF Generation Settings
MAX_PDF_SIZE = int(os.environ.get('MAX_PDF_SIZE', 50 * 1024 * 1024))  # 50MB
PDF_EXPIRATION_HOURS = int(os.environ.get('PDF_EXPIRATION_HOURS', 24))

# Supabase Storage Configuration
SUPABASE_PDF_BUCKET = os.environ.get('SUPABASE_PDF_BUCKET', 'pdf-files')

# Template Configuration
TEMPLATE_CACHE_TIMEOUT = int(os.environ.get('TEMPLATE_CACHE_TIMEOUT', 3600))

# Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... existing apps
    'django_celery_beat',
    'apps.pdf_generation',
]

# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/pdf_generation.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'cvflo.weasyprint': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'cvflo.tasks': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
        'cvflo.template_manager': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

### Celery Configuration

Create `cvflo/__init__.py`:

```python
from .celery_config import app as celery_app

__all__ = ('celery_app',)
```

### Environment Variables

Create `.env` file:

```env
# Django
DEBUG=False
SECRET_KEY=your-secret-key-here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/cvflo

# Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key
SUPABASE_PDF_BUCKET=pdf-files

# Celery & Redis
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# WeasyPrint
WEASYPRINT_MAX_WORKERS=3
WEASYPRINT_TIMEOUT=60
WEASYPRINT_MAX_MEMORY_MB=512

# PDF Settings
MAX_PDF_SIZE=52428800  # 50MB
PDF_EXPIRATION_HOURS=24
```

## Database Migration

Run migrations to create the necessary tables:

```bash
python manage.py makemigrations
python manage.py migrate
```

## Starting Services

### Development

1. **Start Redis** (if not using Docker):
   ```bash
   redis-server
   ```

2. **Start Django development server**:
   ```bash
   python manage.py runserver
   ```

3. **Start Celery worker** (in a new terminal):
   ```bash
   celery -A cvflo worker -l info --concurrency=2
   ```

4. **Start Celery beat** (in a new terminal):
   ```bash
   celery -A cvflo beat -l info
   ```

5. **Start Flower monitoring** (optional):
   ```bash
   celery -A cvflo flower --port=5555
   ```

### Production with Docker

```bash
# Build and start all services
docker-compose -f docker-compose.weasyprint.yml up -d

# View logs
docker-compose -f docker-compose.weasyprint.yml logs -f

# Scale workers
docker-compose -f docker-compose.weasyprint.yml up -d --scale celery-worker=4
```

## API Endpoints

### Synchronous PDF Generation

```http
POST /api/pdf/generate/
Content-Type: application/json
Authorization: Bearer <supabase-jwt>

{
    "cv_data": {
        "personalInfo": {...},
        "workExperience": [...],
        "education": [...],
        "skills": {...}
    },
    "visibility": {
        "personalInfo": true,
        "workExperience": true,
        "education": true,
        "skills": true
    },
    "template_name": "modern-0"
}
```

### Asynchronous PDF Generation

```http
POST /api/pdf/generate-async/
Content-Type: application/json
Authorization: Bearer <supabase-jwt>

{
    "cv_data": {...},
    "visibility": {...},
    "template_name": "modern-0"
}
```

Response:
```json
{
    "task_id": "uuid-task-id",
    "generation_id": "uuid-generation-id",
    "status": "pending",
    "message": "PDF generation started"
}
```

### Check Generation Status

```http
GET /api/pdf/status/{generation_id}/
Authorization: Bearer <supabase-jwt>
```

Response (completed):
```json
{
    "generation_id": "uuid-generation-id",
    "status": "completed",
    "file_url": "https://storage-url/path/to/file.pdf",
    "file_size": 524288,
    "processing_duration": "PT5.2S",
    "created_at": "2024-01-01T12:00:00Z",
    "expires_at": "2024-01-02T12:00:00Z",
    "template_name": "modern-0"
}
```

### Health Check

```http
GET /api/pdf/health/
```

### Template List

```http
GET /api/pdf/templates/
```

## Template Development

### Template Structure

Templates are located in `templates/cv/` directory:

- `modern-0.html` - Modern professional template
- `modern-1.html` - Modern creative template  
- `classic-0.html` - Classic professional template
- `academic-0.html` - Academic research template

### CSS Structure

CSS files are in `static/css/cv/`:

- `base.css` - Common styles and print optimizations
- `{template-name}.css` - Template-specific styles
- `{template-name}-print.css` - Print-specific overrides (optional)

### Template Context

Templates receive the following context:

```python
{
    'cv_data': {
        'personalInfo': {...},
        'workExperience': [...],
        'education': [...],
        'skills': {...},
        # ... other sections
    },
    'visibility': {
        'personalInfo': True,
        'workExperience': True,
        # ... section visibility flags
    },
    'template_name': 'modern-0',
    'template_metadata': {...},
    'template_utils': {
        'format_date': function,
        'format_phone': function,
        'format_url': function,
        # ... utility functions
    },
    'generation_timestamp': datetime,
    'static_url': '/static/'
}
```

## Monitoring and Debugging

### Logs

- Application logs: `logs/pdf_generation.log`
- Celery logs: Check Docker logs or terminal output
- Django logs: Standard Django logging

### Health Checks

```bash
# Check WeasyPrint service health
curl http://localhost:8000/api/pdf/health/

# Check Celery workers
celery -A cvflo inspect ping

# Check Redis connection
redis-cli ping
```

### Monitoring with Flower

Access Flower at `http://localhost:5555` (default credentials: admin/admin123)

### Performance Monitoring

The system includes built-in performance monitoring:

- Memory usage tracking
- PDF generation duration
- Queue lengths and processing times
- Error rates and retry statistics

## Troubleshooting

### Common Issues

1. **WeasyPrint Import Error**
   ```
   Solution: Install system dependencies (cairo, pango, gdk-pixbuf)
   ```

2. **Font Loading Issues**
   ```
   Solution: Install system fonts (fonts-liberation, fonts-dejavu-core)
   ```

3. **Memory Issues**
   ```
   Solution: Reduce WEASYPRINT_MAX_MEMORY_MB, increase swap, or scale horizontally
   ```

4. **Celery Connection Error**
   ```
   Solution: Check Redis connection and CELERY_BROKER_URL configuration
   ```

5. **PDF Generation Timeout**
   ```
   Solution: Increase WEASYPRINT_TIMEOUT or optimize templates
   ```

### Performance Optimization

1. **Template Optimization**
   - Minimize CSS complexity
   - Avoid large images
   - Use efficient selectors
   - Optimize print media queries

2. **Memory Management**
   - Monitor memory usage with psutil
   - Set appropriate worker limits
   - Use memory profiling tools

3. **Scaling**
   - Increase Celery workers
   - Use Redis clustering
   - Implement PDF caching
   - Use CDN for file delivery

## Security Considerations

### File Storage Security

- Files are stored with user-specific paths
- Signed URLs for secure access
- Automatic file expiration
- File size limits enforced

### Input Validation

- CV data validation before processing
- Template name validation
- File type verification
- Rate limiting on API endpoints

### Authentication

- Supabase JWT authentication required
- User isolation in storage paths
- Generation ownership verification

## Maintenance

### Regular Tasks

1. **Cleanup expired PDFs**:
   ```bash
   python manage.py cleanup_generations
   ```

2. **Monitor disk usage**:
   ```bash
   du -sh media/pdfs/
   ```

3. **Check service health**:
   ```bash
   curl http://localhost:8000/api/pdf/health/
   ```

4. **Update dependencies**:
   ```bash
   pip install -U WeasyPrint celery redis
   ```

### Backup and Recovery

1. **Database backups**: Include PDFGenerationLog table
2. **File storage**: Regular Supabase Storage backups
3. **Configuration**: Version control all config files
4. **Templates**: Backup custom templates and CSS

## Support

For issues and questions:

1. Check the logs for error messages
2. Verify all system dependencies are installed
3. Test with simple CV data first
4. Check Redis and Celery connectivity
5. Review the health check endpoint

## Changelog

### Version 1.0.0
- Initial WeasyPrint implementation
- Async processing with Celery
- Template management system
- Supabase Storage integration
- Docker containerization
- Comprehensive monitoring and logging