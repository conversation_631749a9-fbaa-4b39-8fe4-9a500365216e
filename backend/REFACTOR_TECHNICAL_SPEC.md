# CVFlo Backend Refactor - Technical Specification

## Architecture Patterns

### 1. Clean Architecture (Hexagonal Architecture)
```
┌─────────────────────────────────────────────────────────┐
│                    Presentation Layer                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐│
│  │ REST API    │ │ GraphQL     │ │ Background Jobs     ││
│  │ Controllers │ │ Resolvers   │ │ (Celery)           ││
│  └─────────────┘ └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                   Application Layer                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐│
│  │ Use Cases   │ │ DTOs        │ │ Application         ││
│  │ (Interactor)│ │             │ │ Services            ││
│  └─────────────┘ └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                     Domain Layer                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐│
│  │ Entities    │ │ Value       │ │ Domain Services     ││
│  │             │ │ Objects     │ │ & Repositories      ││
│  └─────────────┘ └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────┐
│                 Infrastructure Layer                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────┐│
│  │ Database    │ │ External    │ │ File System         ││
│  │ (Django ORM)│ │ APIs        │ │ & Storage           ││
│  └─────────────┘ └─────────────┘ └─────────────────────┘│
└─────────────────────────────────────────────────────────┘
```

### 2. Domain-Driven Design (DDD)
- **Bounded Contexts**: Authentication, CV Builder, PDF Generation
- **Aggregates**: CV, User, PDFDocument
- **Domain Events**: CVCreated, PDFGenerated, UserAuthenticated
- **Value Objects**: Email, CVId, TemplateId

### 3. CQRS (Command Query Responsibility Segregation)
```python
# Commands (Write operations)
class CreateCVCommand:
    def __init__(self, user_id: str, template_id: str, cv_data: dict):
        self.user_id = user_id
        self.template_id = template_id
        self.cv_data = cv_data

# Queries (Read operations)  
class GetCVQuery:
    def __init__(self, cv_id: str, user_id: str):
        self.cv_id = cv_id
        self.user_id = user_id
```

## Detailed Implementation Plan

### Phase 1: Foundation Setup (Week 1-2)

#### 1.1 Directory Structure Creation
```bash
mkdir -p apps/core/{domain,application,infrastructure,presentation}
mkdir -p apps/core/domain/{entities,value_objects,services,repositories,events}
mkdir -p apps/core/application/{use_cases,dto,interfaces,commands,queries}
mkdir -p apps/core/infrastructure/{persistence,external_services,security,messaging}
mkdir -p apps/core/presentation/{api,middleware,serializers,validators}
```

#### 1.2 Base Classes and Interfaces
```python
# apps/core/domain/base.py
class Entity:
    def __init__(self, entity_id):
        self._id = entity_id
        self._domain_events = []
    
    def add_domain_event(self, event):
        self._domain_events.append(event)
    
    def clear_domain_events(self):
        self._domain_events.clear()

class ValueObject:
    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__
    
    def __hash__(self):
        return hash(tuple(sorted(self.__dict__.items())))

class AggregateRoot(Entity):
    pass

# apps/core/application/base.py
class UseCase(ABC):
    @abstractmethod
    def execute(self, request):
        pass

class Command:
    pass

class Query:
    pass
```

#### 1.3 Dependency Injection Container
```python
# apps/core/infrastructure/di_container.py
from typing import TypeVar, Type, Callable, Dict, Any
from abc import ABC

T = TypeVar('T')

class DIContainer:
    def __init__(self):
        self._services: Dict[Type, Any] = {}
        self._singletons: Dict[Type, Any] = {}
    
    def register_transient(self, interface: Type[T], implementation: Type[T]):
        self._services[interface] = implementation
    
    def register_singleton(self, interface: Type[T], implementation: Type[T]):
        self._services[interface] = implementation
        
    def register_factory(self, interface: Type[T], factory: Callable[[], T]):
        self._services[interface] = factory
    
    def get(self, interface: Type[T]) -> T:
        if interface in self._singletons:
            return self._singletons[interface]
            
        if interface not in self._services:
            raise ValueError(f"Service {interface} not registered")
        
        service = self._services[interface]
        
        if callable(service) and not isinstance(service, type):
            # Factory function
            instance = service()
        else:
            # Class constructor
            instance = service()
        
        # Cache singletons
        if hasattr(service, '_singleton') and service._singleton:
            self._singletons[interface] = instance
            
        return instance

# Global container instance
container = DIContainer()
```

### Phase 2: Authentication Bounded Context (Week 3-4)

#### 2.1 Domain Layer
```python
# apps/authentication/domain/entities/user.py
class User(AggregateRoot):
    def __init__(self, user_id: UserId, email: Email, password_hash: str):
        super().__init__(user_id)
        self._email = email
        self._password_hash = password_hash
        self._is_active = True
        self._created_at = datetime.now()
        self._last_login = None
    
    def authenticate(self, password: str, password_service: PasswordServiceInterface) -> bool:
        if not self._is_active:
            return False
        
        is_valid = password_service.verify(password, self._password_hash)
        if is_valid:
            self._last_login = datetime.now()
            self.add_domain_event(UserAuthenticatedEvent(self._id))
        
        return is_valid
    
    def deactivate(self):
        self._is_active = False
        self.add_domain_event(UserDeactivatedEvent(self._id))

# apps/authentication/domain/value_objects/email.py
class Email(ValueObject):
    def __init__(self, value: str):
        if not self._is_valid_email(value):
            raise ValueError(f"Invalid email: {value}")
        self._value = value.lower()
    
    @property
    def value(self) -> str:
        return self._value
    
    def _is_valid_email(self, email: str) -> bool:
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None

# apps/authentication/domain/repositories/user_repository.py
class UserRepositoryInterface(ABC):
    @abstractmethod
    def save(self, user: User) -> None:
        pass
    
    @abstractmethod
    def find_by_id(self, user_id: UserId) -> Optional[User]:
        pass
    
    @abstractmethod
    def find_by_email(self, email: Email) -> Optional[User]:
        pass
```

#### 2.2 Application Layer
```python
# apps/authentication/application/use_cases/authenticate_user.py
class AuthenticateUserUseCase(UseCase):
    def __init__(self, 
                 user_repository: UserRepositoryInterface,
                 password_service: PasswordServiceInterface,
                 token_service: TokenServiceInterface,
                 event_publisher: EventPublisherInterface):
        self._user_repository = user_repository
        self._password_service = password_service
        self._token_service = token_service
        self._event_publisher = event_publisher
    
    def execute(self, request: AuthenticateUserRequest) -> AuthenticateUserResponse:
        # 1. Find user by email
        user = self._user_repository.find_by_email(Email(request.email))
        if not user:
            raise UserNotFoundException()
        
        # 2. Authenticate user
        if not user.authenticate(request.password, self._password_service):
            raise InvalidCredentialsException()
        
        # 3. Generate token
        token = self._token_service.generate_token(user)
        
        # 4. Save user (for last_login update)
        self._user_repository.save(user)
        
        # 5. Publish domain events
        for event in user.domain_events:
            self._event_publisher.publish(event)
        user.clear_domain_events()
        
        return AuthenticateUserResponse(
            user_id=user.id.value,
            email=user.email.value,
            token=token,
            expires_at=self._token_service.get_expiration(token)
        )

# apps/authentication/application/dto/authenticate_user_request.py
@dataclass
class AuthenticateUserRequest:
    email: str
    password: str
    
    def validate(self):
        if not self.email:
            raise ValidationException("Email is required")
        if not self.password:
            raise ValidationException("Password is required")

@dataclass
class AuthenticateUserResponse:
    user_id: str
    email: str
    token: str
    expires_at: datetime
```

#### 2.3 Infrastructure Layer
```python
# apps/authentication/infrastructure/persistence/django_user_repository.py
class DjangoUserRepository(UserRepositoryInterface):
    def save(self, user: User) -> None:
        user_model, created = DjangoUser.objects.get_or_create(
            id=user.id.value,
            defaults={
                'email': user.email.value,
                'password_hash': user.password_hash,
                'is_active': user.is_active,
                'created_at': user.created_at,
                'last_login': user.last_login
            }
        )
        
        if not created:
            user_model.email = user.email.value
            user_model.is_active = user.is_active
            user_model.last_login = user.last_login
            user_model.save()
    
    def find_by_email(self, email: Email) -> Optional[User]:
        try:
            user_model = DjangoUser.objects.get(email=email.value)
            return self._to_domain_entity(user_model)
        except DjangoUser.DoesNotExist:
            return None
    
    def _to_domain_entity(self, user_model: DjangoUser) -> User:
        return User(
            user_id=UserId(user_model.id),
            email=Email(user_model.email),
            password_hash=user_model.password_hash
        )

# apps/authentication/infrastructure/security/supabase_token_service.py
class SupabaseTokenService(TokenServiceInterface):
    def __init__(self, supabase_client: SupabaseClient):
        self._supabase_client = supabase_client
    
    def generate_token(self, user: User) -> str:
        # Use Supabase to generate JWT token
        response = self._supabase_client.auth.sign_in_with_password({
            'email': user.email.value,
            'password': user.password  # This would need to be handled differently
        })
        return response.session.access_token
    
    def validate_token(self, token: str) -> TokenValidationResult:
        try:
            user_response = self._supabase_client.auth.get_user(token)
            return TokenValidationResult(
                is_valid=True,
                user_id=user_response.user.id,
                email=user_response.user.email
            )
        except Exception:
            return TokenValidationResult(is_valid=False)
```

### Phase 3: Security Consolidation

#### 3.1 Single Security Module
```python
# apps/core/infrastructure/security/__init__.py
from .authentication_service import AuthenticationService
from .authorization_service import AuthorizationService
from .rate_limiter import RateLimiter
from .input_validator import InputValidator

__all__ = [
    'AuthenticationService',
    'AuthorizationService', 
    'RateLimiter',
    'InputValidator'
]

# apps/core/infrastructure/security/authentication_service.py
class AuthenticationService:
    def __init__(self, 
                 token_service: TokenServiceInterface,
                 user_repository: UserRepositoryInterface):
        self._token_service = token_service
        self._user_repository = user_repository
    
    def authenticate_request(self, request: HttpRequest) -> AuthenticationResult:
        token = self._extract_token(request)
        if not token:
            return AuthenticationResult(is_authenticated=False)
        
        validation_result = self._token_service.validate_token(token)
        if not validation_result.is_valid:
            return AuthenticationResult(is_authenticated=False)
        
        user = self._user_repository.find_by_id(UserId(validation_result.user_id))
        if not user or not user.is_active:
            return AuthenticationResult(is_authenticated=False)
        
        return AuthenticationResult(
            is_authenticated=True,
            user=user,
            token=token
        )
```

## Testing Strategy

### Unit Tests
```python
# tests/authentication/domain/test_user.py
class TestUser:
    def test_user_authentication_success(self):
        # Arrange
        user = User(UserId("123"), Email("<EMAIL>"), "hashed_password")
        password_service = Mock(spec=PasswordServiceInterface)
        password_service.verify.return_value = True
        
        # Act
        result = user.authenticate("password", password_service)
        
        # Assert
        assert result is True
        assert len(user.domain_events) == 1
        assert isinstance(user.domain_events[0], UserAuthenticatedEvent)

# tests/authentication/application/test_authenticate_user_use_case.py
class TestAuthenticateUserUseCase:
    def test_successful_authentication(self):
        # Arrange
        user_repository = Mock(spec=UserRepositoryInterface)
        password_service = Mock(spec=PasswordServiceInterface)
        token_service = Mock(spec=TokenServiceInterface)
        event_publisher = Mock(spec=EventPublisherInterface)
        
        use_case = AuthenticateUserUseCase(
            user_repository, password_service, token_service, event_publisher
        )
        
        # Act & Assert
        # ... test implementation
```

This refactor will create a world-class, enterprise-grade backend that's maintainable, testable, and scalable. The architecture follows industry best practices and will serve as a solid foundation for future growth.
