"""
Production Security Hardening for Supabase Integration

This module provides enhanced security measures specifically for Supabase PostgreSQL
connections and authentication, including connection security, rate limiting,
and audit logging.
"""

import logging
import time
import hashlib
import hmac
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import PermissionDenied
from django.http import HttpRequest
from django.utils import timezone
from rest_framework.exceptions import AuthenticationFailed, Throttled
import jwt
from jwt import InvalidTokenError

logger = logging.getLogger('cvflo.security')


class SupabaseSecurityManager:
    """
    Enhanced security manager for Supabase operations
    Provides additional security layers beyond Django's built-in security
    """
    
    def __init__(self):
        self.max_failed_attempts = getattr(settings, 'SUPABASE_MAX_FAILED_ATTEMPTS', 5)
        self.lockout_duration = getattr(settings, 'SUPABASE_LOCKOUT_DURATION', 900)  # 15 minutes
        self.suspicious_activity_threshold = getattr(settings, 'SUSPICIOUS_ACTIVITY_THRESHOLD', 10)

        # Determine key type for authentication strategy
        self.has_publishable_keys = (
            hasattr(settings, 'SUPABASE_PUBLISHABLE_KEY') and
            hasattr(settings, 'SUPABASE_SECRET_KEY') and
            settings.SUPABASE_PUBLISHABLE_KEY and
            settings.SUPABASE_SECRET_KEY
        )
        self.has_jwt_secret = (
            hasattr(settings, 'SUPABASE_JWT_SECRET') and
            settings.SUPABASE_JWT_SECRET and
            settings.SUPABASE_JWT_SECRET != 'cvflo_for_you'
        )
        
    def validate_supabase_jwt(self, token: str, verify_signature: bool = True) -> Dict[str, Any]:
        """
        Enhanced JWT validation with security checks
        
        Args:
            token: JWT token to validate
            verify_signature: Whether to verify token signature
            
        Returns:
            dict: Decoded token payload
            
        Raises:
            AuthenticationFailed: If token is invalid or suspicious
        """
        try:
            # Check token format and length
            if not token or len(token) > 2048:  # Prevent extremely long tokens
                raise AuthenticationFailed('Invalid token format')
            
            # Decode without verification first to check claims
            unverified_payload = jwt.decode(token, options={"verify_signature": False})
            
            # Security checks on token payload
            self._validate_token_claims(unverified_payload)
            
            # Check for token reuse (if enabled)
            if self._is_token_blacklisted(token):
                raise AuthenticationFailed('Token has been revoked')
            
            # If signature verification is required
            if verify_signature:
                # With publishable/secret keys, skip local JWT verification
                # and rely on Supabase API validation
                if self.has_publishable_keys:
                    logger.debug('Using publishable/secret keys - skipping local JWT verification')
                    return unverified_payload

                # For legacy JWT keys, attempt local verification if JWT secret is available
                if self.has_jwt_secret:
                    try:
                        verified_payload = jwt.decode(
                            token,
                            settings.SUPABASE_JWT_SECRET,
                            algorithms=['HS256'],
                            options={
                                'verify_exp': True,
                                'verify_iat': True,
                                'verify_nbf': True,
                            }
                        )
                        logger.debug('Local JWT verification successful')
                        return verified_payload
                    except jwt.InvalidSignatureError:
                        logger.warning('Local JWT signature verification failed - falling back to API validation')
                        return unverified_payload
                else:
                    logger.debug('No JWT secret configured - skipping local verification')
                    return unverified_payload
            
            return unverified_payload
            
        except jwt.ExpiredSignatureError:
            logger.warning(f'Expired JWT token attempt from token ending in: ...{token[-10:]}')
            raise AuthenticationFailed('Token has expired')
        except jwt.InvalidTokenError as e:
            logger.warning(f'Invalid JWT token: {str(e)}')
            raise AuthenticationFailed('Invalid token')
        except Exception as e:
            logger.error(f'JWT validation error: {str(e)}')
            raise AuthenticationFailed('Token validation failed')
    
    def _validate_token_claims(self, payload: Dict[str, Any]):
        """
        Validate JWT token claims for security issues
        
        Args:
            payload: JWT token payload
            
        Raises:
            AuthenticationFailed: If claims are suspicious
        """
        # Check required claims based on token type
        # User tokens require 'sub' (user ID), but anon/service tokens don't
        if payload.get('role') in ['anon', 'service_role']:
            # Project-level tokens (anon/service_role) have different required claims
            required_claims = ['iat', 'exp', 'role']
        else:
            # User tokens require sub (user ID)
            required_claims = ['sub', 'iat', 'exp']

        for claim in required_claims:
            if claim not in payload:
                raise AuthenticationFailed(f'Missing required claim: {claim}')
        
        # Check token age (not too old, not from future)
        now = int(time.time())
        
        # Issued at time check
        iat = payload.get('iat', 0)
        if iat > now + 60:  # Allow 1 minute clock skew
            raise AuthenticationFailed('Token issued in the future')
        
        # Token too old check (24 hours max for user tokens, skip for project tokens)
        if payload.get('role') not in ['anon', 'service_role'] and iat < now - 86400:
            raise AuthenticationFailed('Token too old')
        
        # Expiration check
        exp = payload.get('exp', 0)
        if exp <= now:
            raise AuthenticationFailed('Token expired')
        
        # Check if token is valid for too long (security risk)
        if exp - iat > 86400:  # 24 hours max validity
            logger.warning(f'Long-lived token detected: {exp - iat} seconds')
        
        # Validate user ID format (only for user tokens, not project tokens)
        if payload.get('role') not in ['anon', 'service_role']:
            user_id = payload.get('sub', '')
            if not user_id or len(user_id) != 36:  # UUID length
                raise AuthenticationFailed('Invalid user ID format')
    
    def _is_token_blacklisted(self, token: str) -> bool:
        """
        Check if token is blacklisted
        
        Args:
            token: JWT token to check
            
        Returns:
            bool: True if token is blacklisted
        """
        # Create a hash of the token for privacy
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        cache_key = f'blacklisted_token_{token_hash}'
        
        return cache.get(cache_key, False)
    
    def blacklist_token(self, token: str, ttl: int = 86400):
        """
        Add token to blacklist
        
        Args:
            token: JWT token to blacklist
            ttl: Time to live in seconds
        """
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        cache_key = f'blacklisted_token_{token_hash}'
        cache.set(cache_key, True, timeout=ttl)
        
        logger.info(f'Token blacklisted: {token_hash[:16]}...')
    
    def check_rate_limits(self, request: HttpRequest, user_id: str, action: str = 'api_call'):
        """
        Enhanced rate limiting with user and IP tracking
        
        Args:
            request: HTTP request object
            user_id: User identifier
            action: Action being rate limited
            
        Raises:
            Throttled: If rate limit exceeded
        """
        # Get client IP
        client_ip = self._get_client_ip(request)
        
        # Define rate limits based on action
        rate_limits = {
            'api_call': {'requests': 1000, 'window': 3600},  # 1000 per hour
            'pdf_generation': {'requests': 20, 'window': 3600},  # 20 per hour
            'auth_attempt': {'requests': 10, 'window': 600},  # 10 per 10 minutes
        }
        
        limit_config = rate_limits.get(action, rate_limits['api_call'])
        
        # Check user-based rate limit
        user_key = f'rate_limit_user_{user_id}_{action}'
        user_count = cache.get(user_key, 0)
        
        if user_count >= limit_config['requests']:
            logger.warning(f'Rate limit exceeded for user {user_id}: {action}')
            raise Throttled(detail=f'Rate limit exceeded for {action}')
        
        # Check IP-based rate limit (stricter)
        ip_key = f'rate_limit_ip_{client_ip}_{action}'
        ip_count = cache.get(ip_key, 0)
        ip_limit = min(limit_config['requests'] * 2, 2000)  # 2x user limit, max 2000
        
        if ip_count >= ip_limit:
            logger.warning(f'IP rate limit exceeded for {client_ip}: {action}')
            self._log_suspicious_activity(request, user_id, 'ip_rate_limit_exceeded')
            raise Throttled(detail=f'IP rate limit exceeded for {action}')
        
        # Increment counters
        cache.set(user_key, user_count + 1, timeout=limit_config['window'])
        cache.set(ip_key, ip_count + 1, timeout=limit_config['window'])
    
    def _get_client_ip(self, request: HttpRequest) -> str:
        """
        Get client IP address from request
        
        Args:
            request: HTTP request object
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (when behind proxy/CDN)
        forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if forwarded_for:
            # Get first IP in chain
            return forwarded_for.split(',')[0].strip()
        
        forwarded = request.META.get('HTTP_X_FORWARDED')
        if forwarded:
            return forwarded.strip()
        
        real_ip = request.META.get('HTTP_X_REAL_IP')
        if real_ip:
            return real_ip.strip()
        
        # Fallback to remote addr
        return request.META.get('REMOTE_ADDR', '127.0.0.1')
    
    def check_failed_login_attempts(self, identifier: str, attempt_type: str = 'user') -> bool:
        """
        Check and track failed login attempts
        
        Args:
            identifier: User ID or IP address
            attempt_type: Type of identifier ('user' or 'ip')
            
        Returns:
            bool: True if under limit, False if locked out
        """
        cache_key = f'failed_attempts_{attempt_type}_{identifier}'
        attempts = cache.get(cache_key, 0)
        
        if attempts >= self.max_failed_attempts:
            logger.warning(f'Account locked out: {attempt_type} {identifier}')
            return False
        
        return True
    
    def record_failed_login(self, identifier: str, attempt_type: str = 'user'):
        """
        Record a failed login attempt
        
        Args:
            identifier: User ID or IP address
            attempt_type: Type of identifier ('user' or 'ip')
        """
        cache_key = f'failed_attempts_{attempt_type}_{identifier}'
        attempts = cache.get(cache_key, 0) + 1
        
        cache.set(cache_key, attempts, timeout=self.lockout_duration)
        
        logger.warning(f'Failed login attempt {attempts}/{self.max_failed_attempts}: {attempt_type} {identifier}')
        
        if attempts >= self.max_failed_attempts:
            logger.error(f'Account locked out: {attempt_type} {identifier}')
    
    def clear_failed_attempts(self, identifier: str, attempt_type: str = 'user'):
        """
        Clear failed login attempts (on successful login)
        
        Args:
            identifier: User ID or IP address
            attempt_type: Type of identifier ('user' or 'ip')
        """
        cache_key = f'failed_attempts_{attempt_type}_{identifier}'
        cache.delete(cache_key)
    
    def _log_suspicious_activity(self, request: HttpRequest, user_id: str, activity_type: str):
        """
        Log suspicious activity for monitoring
        
        Args:
            request: HTTP request object
            user_id: User identifier
            activity_type: Type of suspicious activity
        """
        activity_data = {
            'user_id': user_id,
            'ip_address': self._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', '')[:500],  # Limit length
            'activity_type': activity_type,
            'timestamp': timezone.now().isoformat(),
            'path': request.path,
            'method': request.method,
        }
        
        logger.error(f'Suspicious activity detected: {activity_data}')
        
        # Store in cache for immediate analysis
        cache_key = f'suspicious_activity_{user_id}_{int(time.time())}'
        cache.set(cache_key, activity_data, timeout=3600)  # Keep for 1 hour
    
    def validate_database_connection_security(self):
        """
        Validate that database connection is secure
        
        Raises:
            PermissionDenied: If connection is not secure
        """
        db_config = settings.DATABASES['default']
        
        # Check SSL is enabled
        options = db_config.get('OPTIONS', {})
        ssl_mode = options.get('sslmode', 'disable')
        
        if ssl_mode in ['disable', 'allow']:
            if not settings.DEBUG:
                raise PermissionDenied('SSL must be enabled for production database connections')
            logger.warning('Database SSL is disabled - only acceptable in development')
        
        # Check for production database indicators
        host = db_config.get('HOST', '')
        if 'supabase.com' in host and ssl_mode != 'require':
            raise PermissionDenied('Supabase connections must use SSL in require mode')
        
        # Validate connection timeout settings
        connect_timeout = options.get('connect_timeout', 0)
        if connect_timeout == 0 or connect_timeout > 30:
            logger.warning('Database connection timeout should be set between 5-30 seconds')
        
        logger.info('Database connection security validation passed')


class SupabaseAuditLogger:
    """
    Audit logging for Supabase operations
    Tracks important security events and data access
    """
    
    def __init__(self):
        self.audit_logger = logging.getLogger('cvflo.audit')
    
    def log_authentication(self, user_id: str, success: bool, request: HttpRequest, details: Optional[str] = None):
        """
        Log authentication attempts
        
        Args:
            user_id: User identifier
            success: Whether authentication was successful
            request: HTTP request object
            details: Additional details
        """
        client_ip = SupabaseSecurityManager()._get_client_ip(request)
        
        audit_data = {
            'event_type': 'authentication',
            'user_id': user_id,
            'success': success,
            'ip_address': client_ip,
            'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
            'timestamp': timezone.now().isoformat(),
            'details': details,
        }
        
        level = logging.INFO if success else logging.WARNING
        self.audit_logger.log(level, f'Authentication event: {audit_data}')
    
    def log_data_access(self, user_id: str, action: str, resource: str, request: HttpRequest):
        """
        Log data access events
        
        Args:
            user_id: User identifier
            action: Action performed (CREATE, READ, UPDATE, DELETE)
            resource: Resource accessed
            request: HTTP request object
        """
        audit_data = {
            'event_type': 'data_access',
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'ip_address': SupabaseSecurityManager()._get_client_ip(request),
            'timestamp': timezone.now().isoformat(),
        }
        
        self.audit_logger.info(f'Data access event: {audit_data}')
    
    def log_security_event(self, event_type: str, user_id: str, request: HttpRequest, severity: str = 'medium'):
        """
        Log security events
        
        Args:
            event_type: Type of security event
            user_id: User identifier
            request: HTTP request object
            severity: Event severity (low, medium, high, critical)
        """
        audit_data = {
            'event_type': 'security_event',
            'security_event_type': event_type,
            'user_id': user_id,
            'severity': severity,
            'ip_address': SupabaseSecurityManager()._get_client_ip(request),
            'user_agent': request.META.get('HTTP_USER_AGENT', '')[:200],
            'timestamp': timezone.now().isoformat(),
        }
        
        level_map = {
            'low': logging.INFO,
            'medium': logging.WARNING,
            'high': logging.ERROR,
            'critical': logging.CRITICAL,
        }
        
        level = level_map.get(severity, logging.WARNING)
        self.audit_logger.log(level, f'Security event: {audit_data}')


class SupabaseConnectionValidator:
    """
    Validates Supabase connection configuration for security
    """
    
    @staticmethod
    def validate_production_config():
        """
        Validate production configuration
        
        Raises:
            ValueError: If configuration is not production-ready
        """
        errors = []
        
        # Check required environment variables
        required_vars = [
            'SUPABASE_URL',
            'SUPABASE_ANON_KEY',
            'SUPABASE_SERVICE_ROLE_KEY',
            'SUPABASE_DB_PASSWORD',
        ]
        
        for var in required_vars:
            if not getattr(settings, var, ''):
                errors.append(f'{var} is not configured')
        
        # Check URL format
        supabase_url = getattr(settings, 'SUPABASE_URL', '')
        if supabase_url and not supabase_url.startswith('https://'):
            errors.append('SUPABASE_URL must use HTTPS')
        
        # Check key lengths (basic validation)
        anon_key = getattr(settings, 'SUPABASE_ANON_KEY', '')
        if anon_key and len(anon_key) < 100:
            errors.append('SUPABASE_ANON_KEY appears to be invalid (too short)')
        
        service_key = getattr(settings, 'SUPABASE_SERVICE_ROLE_KEY', '')
        if service_key and len(service_key) < 100:
            errors.append('SUPABASE_SERVICE_ROLE_KEY appears to be invalid (too short)')
        
        # Check database configuration
        db_config = settings.DATABASES.get('default', {})
        if db_config.get('ENGINE') == 'django.db.backends.postgresql':
            host = db_config.get('HOST', '')
            if 'supabase.com' in host:
                # Check SSL mode
                ssl_mode = db_config.get('OPTIONS', {}).get('sslmode', '')
                if ssl_mode != 'require':
                    errors.append('Supabase database connections must use sslmode=require')
                
                # Check port (should be 6543 for pooler)
                port = db_config.get('PORT', 5432)
                if port not in [5432, 6543]:
                    errors.append(f'Unexpected database port: {port}')
        
        # Check Django security settings
        if settings.DEBUG and not getattr(settings, 'TESTING', False):
            errors.append('DEBUG should be False in production')
        
        if not settings.SECRET_KEY or 'django-insecure' in settings.SECRET_KEY:
            errors.append('SECRET_KEY must be set to a secure value')
        
        if errors:
            raise ValueError(f'Production configuration errors: {"; ".join(errors)}')
        
        logger.info('Production configuration validation passed')
    
    @staticmethod
    def test_connection_security():
        """
        Test database connection security
        
        Returns:
            dict: Security test results
        """
        results = {
            'ssl_enabled': False,
            'connection_pooling': False,
            'timeout_configured': False,
            'errors': []
        }
        
        try:
            from django.db import connection
            
            with connection.cursor() as cursor:
                # Check SSL
                cursor.execute("SELECT ssl_is_used();")
                ssl_used = cursor.fetchone()[0]
                results['ssl_enabled'] = ssl_used
                
                if not ssl_used:
                    results['errors'].append('SSL is not enabled on database connection')
                
                # Check connection info
                cursor.execute("SELECT current_setting('application_name'), current_setting('connect_timeout');")
                app_name, timeout = cursor.fetchone()
                
                results['connection_pooling'] = 'cvflo' in (app_name or '')
                
                try:
                    timeout_val = int(timeout)
                    results['timeout_configured'] = 5 <= timeout_val <= 30
                except (ValueError, TypeError):
                    results['timeout_configured'] = False
                
        except Exception as e:
            results['errors'].append(f'Connection test failed: {str(e)}')
        
        return results


# Global instances for use throughout the application
security_manager = SupabaseSecurityManager()
audit_logger = SupabaseAuditLogger()
connection_validator = SupabaseConnectionValidator()