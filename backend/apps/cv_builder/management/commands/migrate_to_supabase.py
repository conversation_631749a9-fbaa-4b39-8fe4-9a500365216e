"""
Migration Command: SQLite to Supabase PostgreSQL

This Django management command migrates data from SQLite to Supabase PostgreSQL
while maintaining data integrity and handling UUID field conversions.
"""

import uuid
import json
from typing import Dict, Any, List
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction, connections
from django.conf import settings
from django.contrib.auth.models import User
from apps.cv_builder.models import CVProfile, UserProfile, PDFGenerationLog


class Command(BaseCommand):
    help = 'Migrate data from SQLite to Supabase PostgreSQL database'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--source-db',
            type=str,
            default='sqlite',
            help='Source database alias (default: sqlite)'
        )
        parser.add_argument(
            '--target-db', 
            type=str,
            default='default',
            help='Target database alias (default: default)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without making changes'
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Batch size for data migration (default: 100)'
        )
        parser.add_argument(
            '--skip-users',
            action='store_true',
            help='Skip Django User migration (useful if users are managed by Supabase Auth)'
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force migration even if target database has data'
        )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.migration_stats = {
            'users': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'user_profiles': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'cv_profiles': {'migrated': 0, 'skipped': 0, 'errors': 0},
            'pdf_logs': {'migrated': 0, 'skipped': 0, 'errors': 0},
        }
        self.dry_run = False
    
    def handle(self, *args, **options):
        """Main command handler"""
        self.dry_run = options['dry_run']
        source_db = options['source_db']
        target_db = options['target_db']
        batch_size = options['batch_size']
        skip_users = options['skip_users']
        force = options['force']
        
        self.stdout.write(
            self.style.SUCCESS(
                f'🚀 Starting migration from {source_db} to {target_db}'
            )
        )
        
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('🧪 DRY RUN MODE - No changes will be made')
            )
        
        try:
            # Validate database connections
            self._validate_connections(source_db, target_db)
            
            # Check target database state
            if not force:
                self._check_target_database(target_db)
            
            # Perform migration
            self._migrate_data(source_db, target_db, batch_size, skip_users)
            
            # Print summary
            self._print_migration_summary()
            
            if not self.dry_run:
                self.stdout.write(
                    self.style.SUCCESS('✅ Migration completed successfully!')
                )
            else:
                self.stdout.write(
                    self.style.SUCCESS('✅ Dry run completed successfully!')
                )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Migration failed: {str(e)}')
            )
            raise CommandError(f'Migration failed: {str(e)}')
    
    def _validate_connections(self, source_db: str, target_db: str):
        """Validate database connections"""
        self.stdout.write('🔍 Validating database connections...')
        
        try:
            # Test source connection
            source_conn = connections[source_db]
            with source_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            self.stdout.write(f'✅ Source database ({source_db}) connection OK')
            
            # Test target connection
            target_conn = connections[target_db]
            with target_conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            self.stdout.write(f'✅ Target database ({target_db}) connection OK')
            
        except Exception as e:
            raise CommandError(f'Database connection failed: {str(e)}')
    
    def _check_target_database(self, target_db: str):
        """Check if target database is empty"""
        self.stdout.write('🔍 Checking target database state...')
        
        using_db = target_db
        
        # Check if there's existing data
        existing_data = {
            'users': User.objects.using(using_db).count(),
            'user_profiles': UserProfile.objects.using(using_db).count(),
            'cv_profiles': CVProfile.objects.using(using_db).count(),
            'pdf_logs': PDFGenerationLog.objects.using(using_db).count(),
        }
        
        total_records = sum(existing_data.values())
        
        if total_records > 0:
            self.stdout.write(
                self.style.WARNING(
                    f'⚠️  Target database contains {total_records} existing records:'
                )
            )
            for model, count in existing_data.items():
                if count > 0:
                    self.stdout.write(f'   - {model}: {count}')
            
            raise CommandError(
                'Target database is not empty. Use --force to proceed anyway.'
            )
        else:
            self.stdout.write('✅ Target database is empty, safe to proceed')
    
    def _migrate_data(self, source_db: str, target_db: str, batch_size: int, skip_users: bool):
        """Perform the actual data migration"""
        self.stdout.write('📦 Starting data migration...')
        
        # Migration order is important due to foreign key relationships
        migration_steps = []
        
        if not skip_users:
            migration_steps.append(('users', self._migrate_users))
        
        migration_steps.extend([
            ('user_profiles', self._migrate_user_profiles),
            ('cv_profiles', self._migrate_cv_profiles),
            ('pdf_logs', self._migrate_pdf_logs),
        ])
        
        for step_name, migration_func in migration_steps:
            self.stdout.write(f'📄 Migrating {step_name}...')
            migration_func(source_db, target_db, batch_size)
    
    def _migrate_users(self, source_db: str, target_db: str, batch_size: int):
        """Migrate Django users"""
        try:
            source_users = User.objects.using(source_db).all()
            total_users = source_users.count()
            
            self.stdout.write(f'   Found {total_users} users to migrate')
            
            migrated = 0
            for i in range(0, total_users, batch_size):
                batch_users = source_users[i:i + batch_size]
                
                if not self.dry_run:
                    with transaction.atomic(using=target_db):
                        for user in batch_users:
                            # Create user with same ID to maintain relationships
                            User.objects.using(target_db).create(
                                id=user.id,
                                username=user.username,
                                email=user.email,
                                first_name=user.first_name,
                                last_name=user.last_name,
                                is_active=user.is_active,
                                is_staff=user.is_staff,
                                is_superuser=user.is_superuser,
                                date_joined=user.date_joined,
                                last_login=user.last_login,
                                password=user.password,
                            )
                            migrated += 1
                else:
                    migrated += len(batch_users)
                
                self.stdout.write(f'   Migrated {migrated}/{total_users} users')
            
            self.migration_stats['users']['migrated'] = migrated
            
        except Exception as e:
            self.migration_stats['users']['errors'] += 1
            self.stdout.write(
                self.style.ERROR(f'   ❌ User migration failed: {str(e)}')
            )
            raise
    
    def _migrate_user_profiles(self, source_db: str, target_db: str, batch_size: int):
        """Migrate user profiles"""
        try:
            source_profiles = UserProfile.objects.using(source_db).all()
            total_profiles = source_profiles.count()
            
            self.stdout.write(f'   Found {total_profiles} user profiles to migrate')
            
            migrated = 0
            for i in range(0, total_profiles, batch_size):
                batch_profiles = source_profiles[i:i + batch_size]
                
                if not self.dry_run:
                    with transaction.atomic(using=target_db):
                        for profile in batch_profiles:
                            # Ensure UUID format for id field
                            profile_id = profile.id
                            if isinstance(profile_id, int):
                                # Convert integer ID to UUID
                                profile_id = uuid.uuid4()
                            
                            UserProfile.objects.using(target_db).create(
                                id=profile_id,
                                email=profile.email,
                                full_name=getattr(profile, 'full_name', ''),
                                avatar_url=getattr(profile, 'avatar_url', None),
                                preferences=getattr(profile, 'preferences', {}),
                                created_at=profile.created_at,
                                updated_at=profile.updated_at,
                            )
                            migrated += 1
                else:
                    migrated += len(batch_profiles)
                
                self.stdout.write(f'   Migrated {migrated}/{total_profiles} user profiles')
            
            self.migration_stats['user_profiles']['migrated'] = migrated
            
        except Exception as e:
            self.migration_stats['user_profiles']['errors'] += 1
            self.stdout.write(
                self.style.ERROR(f'   ❌ User profile migration failed: {str(e)}')
            )
            raise
    
    def _migrate_cv_profiles(self, source_db: str, target_db: str, batch_size: int):
        """Migrate CV profiles"""
        try:
            source_cvs = CVProfile.objects.using(source_db).all()
            total_cvs = source_cvs.count()
            
            self.stdout.write(f'   Found {total_cvs} CV profiles to migrate')
            
            migrated = 0
            for i in range(0, total_cvs, batch_size):
                batch_cvs = source_cvs[i:i + batch_size]
                
                if not self.dry_run:
                    with transaction.atomic(using=target_db):
                        for cv in batch_cvs:
                            # Handle UUID conversion for user_id
                            user_id = cv.user_id
                            if isinstance(user_id, int):
                                # Try to find corresponding user
                                try:
                                    django_user = User.objects.using(target_db).get(id=user_id)
                                    user_id = uuid.UUID(django_user.username) if len(django_user.username) == 36 else uuid.uuid4()
                                except User.DoesNotExist:
                                    user_id = uuid.uuid4()
                            
                            # Ensure CV content is valid JSON
                            cv_content = cv.cv_content
                            if isinstance(cv_content, str):
                                try:
                                    cv_content = json.loads(cv_content)
                                except json.JSONDecodeError:
                                    cv_content = {}
                            
                            CVProfile.objects.using(target_db).create(
                                id=cv.id if isinstance(cv.id, uuid.UUID) else uuid.uuid4(),
                                user_id=user_id,
                                cv_content=cv_content,
                                template_name=cv.template_name,
                                created_at=cv.created_at,
                                updated_at=cv.updated_at,
                            )
                            migrated += 1
                else:
                    migrated += len(batch_cvs)
                
                self.stdout.write(f'   Migrated {migrated}/{total_cvs} CV profiles')
            
            self.migration_stats['cv_profiles']['migrated'] = migrated
            
        except Exception as e:
            self.migration_stats['cv_profiles']['errors'] += 1
            self.stdout.write(
                self.style.ERROR(f'   ❌ CV profile migration failed: {str(e)}')
            )
            raise
    
    def _migrate_pdf_logs(self, source_db: str, target_db: str, batch_size: int):
        """Migrate PDF generation logs"""
        try:
            source_pdfs = PDFGenerationLog.objects.using(source_db).all()
            total_pdfs = source_pdfs.count()
            
            self.stdout.write(f'   Found {total_pdfs} PDF logs to migrate')
            
            migrated = 0
            for i in range(0, total_pdfs, batch_size):
                batch_pdfs = source_pdfs[i:i + batch_size]
                
                if not self.dry_run:
                    with transaction.atomic(using=target_db):
                        for pdf_log in batch_pdfs:
                            # Handle UUID conversion for user_id
                            user_id = pdf_log.user_id
                            if isinstance(user_id, int):
                                user_id = uuid.uuid4()
                            
                            # Find corresponding CV profile in target database
                            try:
                                cv_profile = CVProfile.objects.using(target_db).filter(
                                    user_id=user_id
                                ).first()
                                
                                if not cv_profile:
                                    # Skip this PDF log if no corresponding CV profile
                                    self.migration_stats['pdf_logs']['skipped'] += 1
                                    continue
                                
                                PDFGenerationLog.objects.using(target_db).create(
                                    id=pdf_log.id if isinstance(pdf_log.id, uuid.UUID) else uuid.uuid4(),
                                    user_id=user_id,
                                    cv_profile=cv_profile,
                                    template_name=pdf_log.template_name,
                                    status=pdf_log.status,
                                    priority=getattr(pdf_log, 'priority', 'normal'),
                                    task_id=getattr(pdf_log, 'task_id', None),
                                    file_url=getattr(pdf_log, 'file_url', None),
                                    file_path=getattr(pdf_log, 'file_path', None),
                                    file_size=getattr(pdf_log, 'file_size', None),
                                    error_message=getattr(pdf_log, 'error_message', None),
                                    retry_count=getattr(pdf_log, 'retry_count', 0),
                                    max_retries=getattr(pdf_log, 'max_retries', 3),
                                    metadata=getattr(pdf_log, 'metadata', {}),
                                    processing_started_at=getattr(pdf_log, 'processing_started_at', None),
                                    processing_completed_at=getattr(pdf_log, 'processing_completed_at', None),
                                    processing_duration=getattr(pdf_log, 'processing_duration', None),
                                    created_at=pdf_log.created_at,
                                    expires_at=getattr(pdf_log, 'expires_at', None),
                                )
                                migrated += 1
                                
                            except Exception as inner_e:
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'   ⚠️  Skipped PDF log {pdf_log.id}: {str(inner_e)}'
                                    )
                                )
                                self.migration_stats['pdf_logs']['skipped'] += 1
                else:
                    migrated += len(batch_pdfs)
                
                self.stdout.write(f'   Migrated {migrated}/{total_pdfs} PDF logs')
            
            self.migration_stats['pdf_logs']['migrated'] = migrated
            
        except Exception as e:
            self.migration_stats['pdf_logs']['errors'] += 1
            self.stdout.write(
                self.style.ERROR(f'   ❌ PDF log migration failed: {str(e)}')
            )
            raise
    
    def _print_migration_summary(self):
        """Print migration summary"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write('📊 MIGRATION SUMMARY')
        self.stdout.write('='*60)
        
        total_migrated = 0
        total_skipped = 0
        total_errors = 0
        
        for model_name, stats in self.migration_stats.items():
            migrated = stats['migrated']
            skipped = stats['skipped']
            errors = stats['errors']
            
            total_migrated += migrated
            total_skipped += skipped
            total_errors += errors
            
            status_icon = '✅' if errors == 0 else '❌'
            self.stdout.write(f'{status_icon} {model_name.replace("_", " ").title()}:')
            self.stdout.write(f'   Migrated: {migrated}')
            if skipped > 0:
                self.stdout.write(f'   Skipped: {skipped}')
            if errors > 0:
                self.stdout.write(f'   Errors: {errors}')
            self.stdout.write('')
        
        self.stdout.write(f'📈 TOTALS:')
        self.stdout.write(f'   Total Migrated: {total_migrated}')
        if total_skipped > 0:
            self.stdout.write(f'   Total Skipped: {total_skipped}')
        if total_errors > 0:
            self.stdout.write(f'   Total Errors: {total_errors}')
        
        success_rate = (total_migrated / (total_migrated + total_errors)) * 100 if (total_migrated + total_errors) > 0 else 100
        self.stdout.write(f'   Success Rate: {success_rate:.1f}%')
        
        if not self.dry_run and total_errors == 0:
            self.stdout.write('\n💡 NEXT STEPS:')
            self.stdout.write('1. Update your .env file to use DATABASE_ENGINE=supabase')
            self.stdout.write('2. Test your application with the new database')
            self.stdout.write('3. Update any hardcoded references to integer user IDs')
            self.stdout.write('4. Consider backing up your SQLite database before removing it')