"""
Django models for CV Builder app

These models are equivalent to the TypeScript interfaces defined in the Node.js server.
They provide the database schema for storing CV data with proper relationships and validation.
"""

from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator, URLValidator
import uuid

# Use Django's built-in JSO<PERSON>ield (works with both SQLite and PostgreSQL)
try:
    from django.db.models import J<PERSON><PERSON><PERSON>
except ImportError:
    # Fallback for older Django versions
    from django.contrib.postgres.fields import JSONField


class CVProfile(models.Model):
    """
    Main CV profile model that acts as a container for all CV data
    Equivalent to the CVData interface in TypeScript
    Maps to Supabase cv_data table with exact field compatibility
    """
    # Primary key matching Supabase UUID format
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, db_column='id')
    
    # Foreign key to Supabase auth.users table
    user_id = models.UUIDField(db_index=True, db_column='user_id')
    
    # JSON field for CV content - matches Supabase JSONB
    cv_content = models.JSONField(
        default=dict, 
        db_column='cv_content',
        help_text='Complete CV data structure including all sections and visibility settings'
    )
    
    # Template selection
    template_name = models.CharField(
        max_length=50, 
        default='classic-0',
        db_column='template_name',
        choices=[
            ('classic-0', 'Classic Template'),
            ('modern-0', 'Modern Template'),
            ('modern-1', 'Modern Template v2'),
            ('academic-0', 'Academic Template'),
        ]
    )
    
    # Timestamps matching Supabase format
    created_at = models.DateTimeField(auto_now_add=True, db_column='created_at')
    updated_at = models.DateTimeField(auto_now=True, db_column='updated_at')
    
    class Meta:
        db_table = 'cv_data'  # Use Supabase table name
        verbose_name = 'CV Profile'
        verbose_name_plural = 'CV Profiles'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"CV Profile {self.id}"


# Additional models to match Supabase schema

class PDFGenerationLog(models.Model):
    """
    Enhanced PDF Generation Log model with async processing support
    Maps to Supabase pdf_generations table with complete field compatibility
    """
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]
    
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    
    # Primary key matching Supabase
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, db_column='id')
    
    # Foreign keys and references
    user_id = models.UUIDField(db_index=True, db_column='user_id')
    cv_profile = models.ForeignKey(
        'CVProfile',
        on_delete=models.CASCADE,
        related_name='pdf_generations',
        db_column='cv_profile_id',
        null=True,
        blank=True
    )
    template_name = models.CharField(max_length=50, db_column='template_name')
    
    # Async processing fields with Supabase compatibility
    status = models.CharField(
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='pending',
        db_column='status',
        db_index=True
    )
    priority = models.CharField(
        max_length=10, 
        choices=PRIORITY_CHOICES, 
        default='normal',
        db_column='priority'
    )
    task_id = models.CharField(
        max_length=255, 
        blank=True, 
        null=True,
        db_column='task_id',
        db_index=True,
        help_text='Background task identifier (Celery, etc.)'
    )
    
    # File storage with Supabase Storage integration
    file_url = models.URLField(
        blank=True, 
        null=True,
        db_column='file_url',
        help_text='Public URL to generated PDF (Supabase Storage)'
    )
    file_path = models.CharField(
        max_length=500, 
        blank=True, 
        null=True,
        db_column='file_path',
        help_text='Storage path in Supabase Storage bucket'
    )
    file_size = models.PositiveIntegerField(
        null=True, 
        blank=True,
        db_column='file_size',
        help_text='File size in bytes'
    )
    
    # Processing metadata with Supabase column names
    processing_started_at = models.DateTimeField(null=True, blank=True, db_column='processing_started_at')
    processing_completed_at = models.DateTimeField(null=True, blank=True, db_column='processing_completed_at')
    processing_duration = models.DurationField(null=True, blank=True, db_column='processing_duration')
    
    # Error handling
    error_message = models.TextField(blank=True, null=True, db_column='error_message')
    retry_count = models.PositiveIntegerField(default=0, db_column='retry_count')
    max_retries = models.PositiveIntegerField(default=3, db_column='max_retries')
    
    # Additional metadata for Supabase compatibility
    metadata = models.JSONField(
        default=dict, 
        blank=True,
        db_column='metadata',
        help_text='Additional processing metadata and context'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_column='created_at')
    expires_at = models.DateTimeField(null=True, blank=True, db_column='expires_at')
    
    class Meta:
        db_table = 'pdf_generations'  # Use Supabase table name
        verbose_name = 'PDF Generation'
        verbose_name_plural = 'PDF Generations'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user_id', 'status']),
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['created_at']),
            models.Index(fields=['task_id']),
        ]
    
    def __str__(self):
        return f"PDF generation {self.id} ({self.status})"
    
    @property
    def is_processing(self):
        return self.status in ['pending', 'processing']
    
    @property
    def is_completed(self):
        return self.status == 'completed'
    
    @property
    def is_failed(self):
        return self.status == 'failed'
    
    @property
    def can_retry(self):
        return self.is_failed and self.retry_count < self.max_retries


class UserProfile(models.Model):
    """
    User Profile model to match Supabase user_profiles table exactly
    Maps to Supabase user_profiles table with full schema compatibility
    """
    # Primary key - should match Supabase auth.users.id
    id = models.UUIDField(primary_key=True, editable=False, db_column='id')
    
    # User information
    email = models.EmailField(blank=True, db_column='email', db_index=True)
    
    # Additional profile fields that might be needed
    full_name = models.CharField(max_length=255, blank=True, null=True, db_column='full_name')
    avatar_url = models.URLField(blank=True, null=True, db_column='avatar_url')
    
    # Preferences and settings
    preferences = models.JSONField(
        default=dict, 
        blank=True,
        db_column='preferences',
        help_text='User preferences and application settings'
    )
    
    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True, db_column='created_at')
    updated_at = models.DateTimeField(auto_now=True, db_column='updated_at')
    
    class Meta:
        db_table = 'user_profiles'  # Use Supabase table name
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
        ordering = ['-updated_at']
    
    def __str__(self):
        return f"User Profile {self.email or self.id}"


class RateLimitLog(models.Model):
    """
    Rate limiting log for PDF generations with Supabase compatibility
    Tracks user PDF generation requests for rate limiting (5 PDFs/hour per user)
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, db_column='id')
    user_id = models.UUIDField(db_index=True, db_column='user_id')
    action = models.CharField(max_length=50, default='pdf_generation', db_column='action')
    ip_address = models.GenericIPAddressField(null=True, blank=True, db_column='ip_address')
    user_agent = models.TextField(blank=True, db_column='user_agent')
    
    # Additional rate limiting metadata
    endpoint = models.CharField(max_length=100, blank=True, db_column='endpoint')
    request_data = models.JSONField(default=dict, blank=True, db_column='request_data')
    
    created_at = models.DateTimeField(auto_now_add=True, db_column='created_at')
    
    class Meta:
        db_table = 'rate_limit_logs'
        verbose_name = 'Rate Limit Log'
        verbose_name_plural = 'Rate Limit Logs'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user_id', 'action', 'created_at']),
            models.Index(fields=['ip_address', 'created_at']),
        ]
    
    def __str__(self):
        return f"Rate limit log {self.user_id} - {self.action}"