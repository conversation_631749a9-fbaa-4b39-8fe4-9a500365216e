# Generated by Django 5.2.4 on 2025-08-05 00:52

import django.db.models.deletion
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("cv_builder", "0004_alter_cvprofile_user_id_and_more"),
    ]

    operations = [
        migrations.CreateModel(
            name="RateLimitLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        db_column="id",
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("user_id", models.UUIDField(db_column="user_id", db_index=True)),
                (
                    "action",
                    models.CharField(
                        db_column="action", default="pdf_generation", max_length=50
                    ),
                ),
                (
                    "ip_address",
                    models.GenericIPAddressField(
                        blank=True, db_column="ip_address", null=True
                    ),
                ),
                ("user_agent", models.TextField(blank=True, db_column="user_agent")),
                (
                    "endpoint",
                    models.CharField(blank=True, db_column="endpoint", max_length=100),
                ),
                (
                    "request_data",
                    models.J<PERSON>NField(
                        blank=True, db_column="request_data", default=dict
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, db_column="created_at"),
                ),
            ],
            options={
                "verbose_name": "Rate Limit Log",
                "verbose_name_plural": "Rate Limit Logs",
                "db_table": "rate_limit_logs",
                "ordering": ["-created_at"],
            },
        ),
        migrations.AlterModelOptions(
            name="pdfgenerationlog",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "PDF Generation",
                "verbose_name_plural": "PDF Generations",
            },
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="cv_profile",
            field=models.ForeignKey(
                blank=True,
                db_column="cv_profile_id",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="pdf_generations",
                to="cv_builder.cvprofile",
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="error_message",
            field=models.TextField(blank=True, db_column="error_message", null=True),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="expires_at",
            field=models.DateTimeField(blank=True, db_column="expires_at", null=True),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="file_path",
            field=models.CharField(
                blank=True,
                db_column="file_path",
                help_text="Storage path in Supabase Storage bucket",
                max_length=500,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="file_size",
            field=models.PositiveIntegerField(
                blank=True,
                db_column="file_size",
                help_text="File size in bytes",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="file_url",
            field=models.URLField(
                blank=True,
                db_column="file_url",
                help_text="Public URL to generated PDF (Supabase Storage)",
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="max_retries",
            field=models.PositiveIntegerField(db_column="max_retries", default=3),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="metadata",
            field=models.JSONField(
                blank=True,
                db_column="metadata",
                default=dict,
                help_text="Additional processing metadata and context",
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="priority",
            field=models.CharField(
                choices=[
                    ("low", "Low"),
                    ("normal", "Normal"),
                    ("high", "High"),
                    ("urgent", "Urgent"),
                ],
                db_column="priority",
                default="normal",
                max_length=10,
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="processing_completed_at",
            field=models.DateTimeField(
                blank=True, db_column="processing_completed_at", null=True
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="processing_duration",
            field=models.DurationField(
                blank=True, db_column="processing_duration", null=True
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="processing_started_at",
            field=models.DateTimeField(
                blank=True, db_column="processing_started_at", null=True
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="retry_count",
            field=models.PositiveIntegerField(db_column="retry_count", default=0),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="status",
            field=models.CharField(
                choices=[
                    ("pending", "Pending"),
                    ("processing", "Processing"),
                    ("completed", "Completed"),
                    ("failed", "Failed"),
                    ("cancelled", "Cancelled"),
                ],
                db_column="status",
                db_index=True,
                default="pending",
                max_length=20,
            ),
        ),
        migrations.AddField(
            model_name="pdfgenerationlog",
            name="task_id",
            field=models.CharField(
                blank=True,
                db_column="task_id",
                db_index=True,
                help_text="Background task identifier (Celery, etc.)",
                max_length=255,
                null=True,
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="avatar_url",
            field=models.URLField(blank=True, db_column="avatar_url", null=True),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="full_name",
            field=models.CharField(
                blank=True, db_column="full_name", max_length=255, null=True
            ),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="preferences",
            field=models.JSONField(
                blank=True,
                db_column="preferences",
                default=dict,
                help_text="User preferences and application settings",
            ),
        ),
        migrations.AlterField(
            model_name="cvprofile",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_column="created_at"),
        ),
        migrations.AlterField(
            model_name="cvprofile",
            name="cv_content",
            field=models.JSONField(
                db_column="cv_content",
                default=dict,
                help_text="Complete CV data structure including all sections and visibility settings",
            ),
        ),
        migrations.AlterField(
            model_name="cvprofile",
            name="id",
            field=models.UUIDField(
                db_column="id",
                default=uuid.uuid4,
                editable=False,
                primary_key=True,
                serialize=False,
            ),
        ),
        migrations.AlterField(
            model_name="cvprofile",
            name="template_name",
            field=models.CharField(
                choices=[
                    ("classic-0", "Classic Template"),
                    ("modern-0", "Modern Template"),
                    ("modern-1", "Modern Template v2"),
                    ("academic-0", "Academic Template"),
                ],
                db_column="template_name",
                default="classic-0",
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name="cvprofile",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_column="updated_at"),
        ),
        migrations.AlterField(
            model_name="cvprofile",
            name="user_id",
            field=models.UUIDField(db_column="user_id", db_index=True),
        ),
        migrations.AlterField(
            model_name="pdfgenerationlog",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_column="created_at"),
        ),
        migrations.AlterField(
            model_name="pdfgenerationlog",
            name="id",
            field=models.UUIDField(
                db_column="id",
                default=uuid.uuid4,
                editable=False,
                primary_key=True,
                serialize=False,
            ),
        ),
        migrations.AlterField(
            model_name="pdfgenerationlog",
            name="template_name",
            field=models.CharField(db_column="template_name", max_length=50),
        ),
        migrations.AlterField(
            model_name="pdfgenerationlog",
            name="user_id",
            field=models.UUIDField(db_column="user_id", db_index=True),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="created_at",
            field=models.DateTimeField(auto_now_add=True, db_column="created_at"),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="email",
            field=models.EmailField(
                blank=True, db_column="email", db_index=True, max_length=254
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="id",
            field=models.UUIDField(
                db_column="id", editable=False, primary_key=True, serialize=False
            ),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="updated_at",
            field=models.DateTimeField(auto_now=True, db_column="updated_at"),
        ),
        migrations.AddIndex(
            model_name="pdfgenerationlog",
            index=models.Index(
                fields=["user_id", "status"], name="pdf_generat_user_id_a200a3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pdfgenerationlog",
            index=models.Index(
                fields=["status", "priority"], name="pdf_generat_status_515b00_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pdfgenerationlog",
            index=models.Index(
                fields=["created_at"], name="pdf_generat_created_7bde45_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="pdfgenerationlog",
            index=models.Index(
                fields=["task_id"], name="pdf_generat_task_id_3c268a_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="ratelimitlog",
            index=models.Index(
                fields=["user_id", "action", "created_at"],
                name="rate_limit__user_id_63103c_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="ratelimitlog",
            index=models.Index(
                fields=["ip_address", "created_at"],
                name="rate_limit__ip_addr_bb0f2b_idx",
            ),
        ),
    ]
