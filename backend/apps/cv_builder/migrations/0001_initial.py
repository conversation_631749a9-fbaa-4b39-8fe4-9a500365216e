# Generated by Django 5.0.9 on 2025-07-30 15:34

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="CVProfile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("user_id", models.UUIDField()),
                ("cv_content", models.<PERSON><PERSON><PERSON>ield(default=dict)),
                ("template_name", models.Char<PERSON>ield(default="classic-0", max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "CV Profile",
                "verbose_name_plural": "CV Profiles",
                "db_table": "cv_data",
                "ordering": ["-updated_at"],
            },
        ),
    ]
