# Generated by Django 5.0.9 on 2025-07-30 15:42

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("cv_builder", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PDFGenerationLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("user_id", models.UUIDField()),
                ("template_name", models.CharField(max_length=50)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "PDF Generation Log",
                "verbose_name_plural": "PDF Generation Logs",
                "db_table": "pdf_generations",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("email", models.<PERSON>ailField(blank=True, max_length=254)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
                "db_table": "user_profiles",
                "ordering": ["-updated_at"],
            },
        ),
    ]
