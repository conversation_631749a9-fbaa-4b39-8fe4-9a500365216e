"""
Supabase Storage Service for secure PDF file management
"""

import logging
import hashlib
import mimetypes
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from django.conf import settings
from supabase import create_client, Client
from .exceptions import StorageError

logger = logging.getLogger('cvflo.pdf_generation')


class SupabaseStorageService:
    """
    Service for managing PDF files in Supabase Storage
    Provides secure upload, download, and deletion of PDF files
    """
    
    def __init__(self):
        self.supabase_url = settings.SUPABASE_URL
        self.supabase_key = settings.SUPABASE_ANON_KEY
        self.bucket_name = getattr(settings, 'SUPABASE_PDF_BUCKET', 'pdf-files')
        self.client: Client = create_client(self.supabase_url, self.supabase_key)
        self.storage = self.client.storage
    
    def upload_pdf(self, pdf_bytes: bytes, filename: str, user_id: str, 
                   generation_id: str, content_type: str = 'application/pdf') -> tuple[str, str]:
        """
        Upload PDF to Supabase Storage with secure path generation
        
        Args:
            pdf_bytes: PDF file content as bytes
            filename: Original filename
            user_id: User UUID for path organization
            generation_id: Generation UUID for unique identification
            content_type: MIME type of the file
            
        Returns:
            tuple: (public_url, file_path) of uploaded file
            
        Raises:
            StorageError: If upload fails
        """
        try:
            # Generate secure file path
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y/%m/%d')
            sanitized_filename = self._sanitize_filename(filename)
            file_path = f"users/{user_id}/pdfs/{timestamp}/{generation_id}_{sanitized_filename}"
            
            logger.info(f'Uploading PDF to Supabase Storage: {file_path}')
            
            # Validate file content
            if not pdf_bytes:
                raise StorageError("Empty PDF buffer")
            
            max_size = getattr(settings, 'MAX_PDF_SIZE', 50 * 1024 * 1024)  # 50MB default
            if len(pdf_bytes) > max_size:
                raise StorageError(f"PDF file too large: {len(pdf_bytes)} bytes")
            
            # Generate file metadata
            file_hash = hashlib.sha256(pdf_bytes).hexdigest()
            
            # Upload to Supabase Storage
            response = self.storage.from_(self.bucket_name).upload(
                path=file_path,
                file=pdf_bytes,
                file_options={
                    'content-type': content_type,
                    'cache-control': '3600',
                    'x-file-hash': file_hash,
                    'x-user-id': user_id,
                    'x-generation-id': generation_id
                }
            )
            
            if response.status_code not in [200, 201]:
                error_msg = f"Upload failed: {response.status_code}"
                if hasattr(response, 'error'):
                    error_msg += f" - {response.error}"
                raise StorageError(error_msg)
            
            # Get public URL
            public_url = self.get_public_url(file_path)
            
            logger.info(f'PDF uploaded successfully: {file_path}, size: {len(pdf_bytes)} bytes')
            return public_url, file_path
            
        except Exception as e:
            logger.error(f'PDF upload failed: {str(e)}')
            if isinstance(e, StorageError):
                raise
            raise StorageError(f'Upload failed: {str(e)}')
    
    def get_public_url(self, file_path: str) -> str:
        """
        Get public URL for a file in storage
        
        Args:
            file_path: Path to the file
            
        Returns:
            str: Public URL
        """
        try:
            response = self.storage.from_(self.bucket_name).get_public_url(file_path)
            return response
            
        except Exception as e:
            logger.error(f'Failed to get public URL for {file_path}: {str(e)}')
            raise StorageError(f'Failed to get public URL: {str(e)}')
    
    def get_signed_url(self, file_path: str, expires_in: int = 3600) -> str:
        """
        Get signed URL for secure file access
        
        Args:
            file_path: Path to the file
            expires_in: URL expiration time in seconds
            
        Returns:
            str: Signed URL
        """
        try:
            response = self.storage.from_(self.bucket_name).create_signed_url(
                path=file_path,
                expires_in=expires_in
            )
            
            if response.get('error'):
                raise StorageError(f"Failed to create signed URL: {response['error']}")
            
            return response['signedURL']
            
        except Exception as e:
            logger.error(f'Failed to create signed URL for {file_path}: {str(e)}')
            if isinstance(e, StorageError):
                raise
            raise StorageError(f'Failed to create signed URL: {str(e)}')
    
    def delete_file(self, file_path: str) -> bool:
        """
        Delete file from storage
        
        Args:
            file_path: Path to the file to delete
            
        Returns:
            bool: True if deletion was successful
            
        Raises:
            StorageError: If deletion fails
        """
        try:
            logger.info(f'Deleting file from Supabase Storage: {file_path}')
            
            response = self.storage.from_(self.bucket_name).remove([file_path])
            
            if response.status_code not in [200, 204]:
                error_msg = f"Deletion failed: {response.status_code}"
                if hasattr(response, 'error'):
                    error_msg += f" - {response.error}"
                raise StorageError(error_msg)
            
            logger.info(f'File deleted successfully: {file_path}')
            return True
            
        except Exception as e:
            logger.error(f'File deletion failed: {str(e)}')
            if isinstance(e, StorageError):
                raise
            raise StorageError(f'Deletion failed: {str(e)}')
    
    def file_exists(self, file_path: str) -> bool:
        """
        Check if file exists in storage
        
        Args:
            file_path: Path to check
            
        Returns:
            bool: True if file exists
        """
        try:
            response = self.storage.from_(self.bucket_name).list(
                path=file_path,
                limit=1
            )
            
            return len(response) > 0
            
        except Exception as e:
            logger.warning(f'Failed to check file existence for {file_path}: {str(e)}')
            return False
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get file metadata
        
        Args:
            file_path: Path to the file
            
        Returns:
            dict: File metadata or None if not found
        """
        try:
            # Get file info from bucket
            folder_path = '/'.join(file_path.split('/')[:-1]) if '/' in file_path else ''
            file_name = file_path.split('/')[-1]
            
            response = self.storage.from_(self.bucket_name).list(path=folder_path)
            
            for file_info in response:
                if file_info['name'] == file_name:
                    return {
                        'name': file_info['name'],
                        'size': file_info.get('metadata', {}).get('size'),
                        'content_type': file_info.get('metadata', {}).get('mimetype'),
                        'last_modified': file_info.get('updated_at'),
                        'path': file_path
                    }
            
            return None
            
        except Exception as e:
            logger.warning(f'Failed to get file info for {file_path}: {str(e)}')
            return None
    
    def cleanup_old_files(self, days_old: int = 30) -> int:
        """
        Clean up files older than specified days
        
        Args:
            days_old: Delete files older than this many days
            
        Returns:
            int: Number of files deleted
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            deleted_count = 0
            
            # List all files in the bucket
            response = self.storage.from_(self.bucket_name).list()
            
            for file_info in response:
                if file_info.get('updated_at'):
                    file_date = datetime.fromisoformat(file_info['updated_at'].replace('Z', '+00:00'))
                    if file_date < cutoff_date:
                        try:
                            self.delete_file(file_info['name'])
                            deleted_count += 1
                        except Exception as e:
                            logger.warning(f'Failed to delete old file {file_info["name"]}: {str(e)}')
            
            logger.info(f'Cleaned up {deleted_count} old files')
            return deleted_count
            
        except Exception as e:
            logger.error(f'Failed to cleanup old files: {str(e)}')
            return 0
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """
        Get storage usage statistics
        
        Returns:
            dict: Storage statistics
        """
        try:
            # List all files to calculate stats
            response = self.storage.from_(self.bucket_name).list()
            
            total_files = len(response)
            total_size = 0
            
            for file_info in response:
                if file_info.get('metadata', {}).get('size'):
                    total_size += int(file_info['metadata']['size'])
            
            return {
                'bucket_name': self.bucket_name,
                'total_files': total_files,
                'total_size_bytes': total_size,
                'total_size_mb': round(total_size / (1024 * 1024), 2),
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f'Failed to get storage stats: {str(e)}')
            return {
                'bucket_name': self.bucket_name,
                'error': str(e)
            }
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename for safe storage
        
        Args:
            filename: Original filename
            
        Returns:
            str: Sanitized filename
        """
        import re
        
        # Remove or replace unsafe characters
        sanitized = re.sub(r'[^\w\-_\.]', '_', filename)
        
        # Ensure filename is not too long
        max_length = 100
        if len(sanitized) > max_length:
            name, ext = sanitized.rsplit('.', 1) if '.' in sanitized else (sanitized, '')
            sanitized = name[:max_length-len(ext)-1] + '.' + ext if ext else name[:max_length]
        
        # Ensure filename is not empty
        if not sanitized or sanitized == '.':
            sanitized = 'document.pdf'
        
        return sanitized