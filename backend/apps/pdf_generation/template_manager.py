"""
Template Management System for CV PDF Generation

This module provides a robust template management system for CV layouts,
including template loading, caching, validation, and rendering with proper
print media queries and CSS optimization.
"""

import logging
import os
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from django.template.loader import render_to_string, get_template
from django.template import Template, Context, TemplateDoesNotExist
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

from .exceptions import TemplateNotFoundError, PDFGenerationError

logger = logging.getLogger('cvflo.template_manager')


class TemplateManager:
    """
    Template management system for CV PDF generation
    
    Features:
    - Template discovery and validation
    - CSS optimization for print media
    - Template caching for performance
    - Context preparation and data filtering
    - Print-specific layout handling
    """
    
    # Available templates with metadata
    TEMPLATE_REGISTRY = {
        'modern-0': {
            'name': 'Modern Professional',
            'description': 'Clean, modern design with subtle colors',
            'category': 'modern',
            'features': ['two-column', 'color-accent', 'icons'],
            'preview_image': 'previews/modern-0.png',
        },
        'modern-1': {
            'name': 'Modern Creative',
            'description': 'Contemporary design with bold typography',
            'category': 'modern',
            'features': ['single-column', 'bold-headers', 'color-blocks'],
            'preview_image': 'previews/modern-1.png',
        },
        'classic-0': {
            'name': 'Classic Professional',
            'description': 'Traditional, conservative layout',
            'category': 'classic',
            'features': ['single-column', 'serif-fonts', 'minimal'],
            'preview_image': 'previews/classic-0.png',
        },
        'academic-0': {
            'name': 'Academic Research',
            'description': 'Scholarly format for academic positions',
            'category': 'academic',
            'features': ['detailed-sections', 'publications', 'references'],
            'preview_image': 'previews/academic-0.png',
        },
    }
    
    def __init__(self):
        self.template_dir = self._get_template_directory()
        self.css_dir = self._get_css_directory()
        self.cache_timeout = getattr(settings, 'TEMPLATE_CACHE_TIMEOUT', 3600)  # 1 hour
        
        # Validate template directory
        if not self.template_dir.exists():
            logger.warning(f"Template directory not found: {self.template_dir}")
        
        logger.info(f"Template manager initialized with directory: {self.template_dir}")
    
    def _get_template_directory(self) -> Path:
        """Get the template directory path"""
        # Get the templates directory from Django settings
        template_dirs = getattr(settings, 'TEMPLATES', [{}])
        if template_dirs and 'DIRS' in template_dirs[0] and template_dirs[0]['DIRS']:
            base_templates_dir = template_dirs[0]['DIRS'][0]
        else:
            base_templates_dir = settings.BASE_DIR / 'templates'

        # Get the CV templates subdirectory from PDF_GENERATION settings
        cv_templates_subdir = getattr(settings, 'PDF_GENERATION', {}).get('TEMPLATES_DIR', 'cv')

        return Path(base_templates_dir) / cv_templates_subdir
    
    def _get_css_directory(self) -> Path:
        """Get the CSS directory path"""
        return Path(settings.STATIC_ROOT or settings.BASE_DIR) / 'css' / 'cv'
    
    def template_exists(self, template_name: str) -> bool:
        """Check if a template exists"""
        if template_name not in self.TEMPLATE_REGISTRY:
            return False
        
        template_path = self.template_dir / f"{template_name}.html"
        return template_path.exists()
    
    def get_template_list(self) -> List[Dict[str, Any]]:
        """Get list of available templates with metadata"""
        templates = []
        
        for template_id, metadata in self.TEMPLATE_REGISTRY.items():
            if self.template_exists(template_id):
                template_info = {
                    'id': template_id,
                    'available': True,
                    **metadata
                }
                templates.append(template_info)
            else:
                logger.warning(f"Template {template_id} registered but file not found")
        
        return templates
    
    def render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """
        Render CV template with context data
        
        Args:
            template_name: Template identifier
            context: Template context data
            
        Returns:
            str: Rendered HTML content
            
        Raises:
            TemplateNotFoundError: If template is not found
            PDFGenerationError: If rendering fails
        """
        try:
            # Check if template exists
            if not self.template_exists(template_name):
                raise TemplateNotFoundError(f"Template '{template_name}' not found")
            
            # Prepare enhanced context
            enhanced_context = self._prepare_context(template_name, context)
            
            # Check cache first
            cache_key = self._get_cache_key(template_name, enhanced_context)
            cached_html = cache.get(cache_key)
            
            if cached_html:
                logger.debug(f"Template {template_name} served from cache")
                return cached_html
            
            # Render template
            template_path = f"cv/{template_name}.html"
            html_content = render_to_string(template_path, enhanced_context)
            
            # Process and optimize HTML
            processed_html = self._process_rendered_html(html_content, template_name)
            
            # Cache the result
            cache.set(cache_key, processed_html, self.cache_timeout)
            
            logger.info(f"Template {template_name} rendered successfully")
            return processed_html
            
        except TemplateDoesNotExist:
            raise TemplateNotFoundError(f"Template file for '{template_name}' not found")
        except Exception as e:
            logger.error(f"Template rendering failed for {template_name}: {str(e)}")
            raise PDFGenerationError(f"Failed to render template {template_name}: {str(e)}")
    
    def get_template_css(self, template_name: str) -> str:
        """
        Get CSS content for template with print media optimization
        
        Args:
            template_name: Template identifier
            
        Returns:
            str: CSS content optimized for print
        """
        try:
            # Check cache first
            cache_key = f"template_css_{template_name}"
            cached_css = cache.get(cache_key)
            
            if cached_css:
                return cached_css
            
            # Load base CSS
            base_css = self._load_base_css()
            
            # Load template-specific CSS
            template_css = self._load_template_css(template_name)
            
            # Load print-specific CSS
            print_css = self._load_print_css(template_name)
            
            # Combine CSS
            combined_css = self._combine_css_files([
                base_css,
                template_css,
                print_css
            ])
            
            # Optimize for print
            optimized_css = self._optimize_print_css(combined_css)
            
            # Cache the result
            cache.set(cache_key, optimized_css, self.cache_timeout)
            
            return optimized_css
            
        except Exception as e:
            logger.warning(f"Failed to load CSS for template {template_name}: {e}")
            return self._get_fallback_css()
    
    def _prepare_context(self, template_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare enhanced context for template rendering"""
        enhanced_context = context.copy()
        
        # Add template metadata
        enhanced_context['template_metadata'] = self.TEMPLATE_REGISTRY.get(template_name, {})
        
        # Add utility functions
        enhanced_context['template_utils'] = {
            'format_date': self._format_date,
            'format_phone': self._format_phone,
            'format_url': self._format_url,
            'truncate_text': self._truncate_text,
            'get_section_title': self._get_section_title,
        }
        
        # Add template-specific data processing
        enhanced_context = self._process_template_data(template_name, enhanced_context)
        
        return enhanced_context
    
    def _process_template_data(self, template_name: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Process CV data for specific template requirements"""
        cv_data = context.get('cv_data', {})
        
        # Template-specific processing
        if template_name == 'academic-0':
            context = self._process_academic_template(context, cv_data)
        elif template_name.startswith('modern-'):
            context = self._process_modern_template(context, cv_data)
        elif template_name == 'classic-0':
            context = self._process_classic_template(context, cv_data)
        
        return context
    
    def _process_academic_template(self, context: Dict[str, Any], cv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process data for academic template"""
        # Sort publications by date
        if 'publications' in cv_data:
            publications = cv_data['publications']
            if isinstance(publications, list):
                publications.sort(key=lambda x: x.get('date', ''), reverse=True)
        
        # Group education by level
        if 'education' in cv_data:
            education = cv_data['education']
            if isinstance(education, list):
                context['education_grouped'] = self._group_education_by_level(education)
        
        return context
    
    def _process_modern_template(self, context: Dict[str, Any], cv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process data for modern templates"""
        # Calculate skill levels for visual representation
        if 'skills' in cv_data:
            skills = cv_data['skills']
            context['skills_with_levels'] = self._process_skills_with_levels(skills)
        
        return context
    
    def _process_classic_template(self, context: Dict[str, Any], cv_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process data for classic template"""
        # Simplify data presentation for conservative layout
        return context
    
    def _load_base_css(self) -> str:
        """Load base CSS common to all templates"""
        try:
            css_path = self.css_dir / 'base.css'
            if css_path.exists():
                return css_path.read_text(encoding='utf-8')
        except Exception as e:
            logger.warning(f"Failed to load base CSS: {e}")
        
        return self._get_base_css_content()
    
    def _load_template_css(self, template_name: str) -> str:
        """Load template-specific CSS"""
        try:
            css_path = self.css_dir / f"{template_name}.css"
            if css_path.exists():
                return css_path.read_text(encoding='utf-8')
        except Exception as e:
            logger.warning(f"Failed to load template CSS for {template_name}: {e}")
        
        return ""
    
    def _load_print_css(self, template_name: str) -> str:
        """Load print-specific CSS"""
        try:
            css_path = self.css_dir / f"{template_name}-print.css"
            if css_path.exists():
                return css_path.read_text(encoding='utf-8')
        except Exception as e:
            logger.debug(f"No print CSS found for {template_name}")
        
        return self._get_print_css_content()
    
    def _combine_css_files(self, css_contents: List[str]) -> str:
        """Combine multiple CSS contents"""
        return '\n\n'.join(filter(None, css_contents))
    
    def _optimize_print_css(self, css_content: str) -> str:
        """Optimize CSS for print media"""
        # Add print media queries and optimizations
        optimizations = [
            # Remove animations and transitions
            "* { -webkit-animation: none !important; animation: none !important; transition: none !important; }",
            
            # Optimize colors for print
            "@media print { .color-accent { color: #333 !important; } }",
            
            # Page break optimizations
            "@media print { h1, h2, h3 { page-break-after: avoid; } }",
            "@media print { p, li { orphans: 2; widows: 2; } }",
            
            # Hide interactive elements
            "@media print { button, input, select, textarea { display: none !important; } }",
        ]
        
        return css_content + '\n' + '\n'.join(optimizations)
    
    def _process_rendered_html(self, html_content: str, template_name: str) -> str:
        """Process and optimize rendered HTML"""
        # Add meta tags for PDF generation
        if '<head>' in html_content:
            meta_tags = '''
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <meta name="pdf-generation" content="weasyprint">
            '''
            html_content = html_content.replace('<head>', f'<head>{meta_tags}')
        
        return html_content
    
    def _get_cache_key(self, template_name: str, context: Dict[str, Any]) -> str:
        """Generate cache key for template rendering"""
        import hashlib
        
        # Create hash from context (excluding timestamp and other volatile data)
        stable_context = {k: v for k, v in context.items() 
                         if k not in ['generation_timestamp', 'template_utils']}
        
        context_hash = hashlib.md5(str(sorted(stable_context.items())).encode()).hexdigest()
        return f"template_{template_name}_{context_hash}"
    
    def _get_base_css_content(self) -> str:
        """Get default base CSS content"""
        return """
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #333;
            background: white;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin-bottom: 0.5em;
            font-weight: bold;
        }
        
        h1 { font-size: 24pt; }
        h2 { font-size: 18pt; }
        h3 { font-size: 14pt; }
        h4 { font-size: 12pt; }
        
        p, li {
            margin-bottom: 0.5em;
        }
        
        ul, ol {
            margin-left: 1.5em;
            margin-bottom: 1em;
        }
        
        .section {
            margin-bottom: 2em;
        }
        
        .section-title {
            border-bottom: 2px solid #333;
            padding-bottom: 0.2em;
            margin-bottom: 1em;
            font-size: 14pt;
            font-weight: bold;
        }
        
        .contact-info {
            text-align: center;
            margin-bottom: 2em;
        }
        
        .experience-item,
        .education-item {
            margin-bottom: 1.5em;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 0.5em;
        }
        
        .item-title {
            font-weight: bold;
        }
        
        .item-date {
            font-style: italic;
            color: #666;
        }
        
        .item-company,
        .item-institution {
            color: #666;
            margin-bottom: 0.3em;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1em;
        }
        
        .skill-category {
            margin-bottom: 1em;
        }
        
        .skill-category-title {
            font-weight: bold;
            margin-bottom: 0.5em;
        }
        
        .skill-list {
            list-style: none;
            margin-left: 0;
        }
        
        .skill-item {
            padding: 0.2em 0;
        }
        """
    
    def _get_print_css_content(self) -> str:
        """Get default print CSS content"""
        return """
        @page {
            size: A4;
            margin: 20mm;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .cv-container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .no-break {
                page-break-inside: avoid;
            }
            
            .section {
                page-break-inside: avoid;
            }
            
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid;
            }
            
            .experience-item,
            .education-item {
                page-break-inside: avoid;
            }
        }
        """
    
    def _get_fallback_css(self) -> str:
        """Get fallback CSS when loading fails"""
        return self._get_base_css_content() + self._get_print_css_content()
    
    # Utility functions for templates
    def _format_date(self, date_str: str) -> str:
        """Format date string for display"""
        if not date_str:
            return ""
        
        try:
            from datetime import datetime
            date_obj = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
            return date_obj.strftime('%B %Y')
        except Exception:
            return date_str
    
    def _format_phone(self, phone: str) -> str:
        """Format phone number for display"""
        if not phone:
            return ""
        
        # Simple phone formatting
        digits = ''.join(filter(str.isdigit, phone))
        if len(digits) == 10:
            return f"({digits[:3]}) {digits[3:6]}-{digits[6:]}"
        return phone
    
    def _format_url(self, url: str) -> str:
        """Format URL for display"""
        if not url:
            return ""
        
        # Remove protocol for cleaner display
        return url.replace('https://', '').replace('http://', '').replace('www.', '')
    
    def _truncate_text(self, text: str, max_length: int = 100) -> str:
        """Truncate text to specified length"""
        if not text or len(text) <= max_length:
            return text
        
        return text[:max_length-3] + "..."
    
    def _get_section_title(self, section_key: str) -> str:
        """Get human-readable section title"""
        section_titles = {
            'personalInfo': 'Personal Information',
            'summary': 'Professional Summary',
            'workExperience': 'Work Experience',
            'education': 'Education',
            'skills': 'Skills',
            'projects': 'Projects',
            'certifications': 'Certifications',
            'languages': 'Languages',
            'interests': 'Interests',
            'references': 'References',
            'publications': 'Publications',
        }
        
        return section_titles.get(section_key, section_key.title())
    
    def _group_education_by_level(self, education: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """Group education entries by level"""
        grouped = {}
        
        for edu in education:
            level = edu.get('degree', 'Other')
            if level not in grouped:
                grouped[level] = []
            grouped[level].append(edu)
        
        return grouped
    
    def _process_skills_with_levels(self, skills: Dict[str, Any]) -> Dict[str, Any]:
        """Process skills with level indicators"""
        processed_skills = {}
        
        for category, skill_list in skills.items():
            if isinstance(skill_list, list):
                processed_skills[category] = []
                for skill in skill_list:
                    if isinstance(skill, dict) and 'level' in skill:
                        skill['level_percentage'] = self._convert_skill_level(skill['level'])
                    processed_skills[category].append(skill)
            else:
                processed_skills[category] = skill_list
        
        return processed_skills
    
    def _convert_skill_level(self, level: str) -> int:
        """Convert skill level to percentage"""
        level_map = {
            'beginner': 25,
            'intermediate': 50,
            'advanced': 75,
            'expert': 100,
        }
        
        return level_map.get(level.lower(), 50)