"""
Security utilities for PDF generation system
Includes JWT authentication, rate limiting, and input validation
"""

import logging
import hashlib
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache
from django.http import HttpRequest
from apps.cv_builder.models import RateLimitLog, PDFGenerationLog
from .exceptions import RateLimitExceededError, InvalidCVDataError
import jwt
from supabase import create_client

logger = logging.getLogger('cvflo.pdf_generation')


class SupabaseJWTAuth:
    """
    JWT authentication using Supabase tokens
    """
    
    def __init__(self):
        self.supabase_url = settings.SUPABASE_URL

        # Support both legacy and new key types
        self.publishable_key = getattr(settings, 'SUPABASE_PUBLISHABLE_KEY', '')
        self.secret_key = getattr(settings, 'SUPABASE_SECRET_KEY', '')
        self.anon_key = getattr(settings, 'SUPABASE_ANON_KEY', '')
        self.jwt_secret = getattr(settings, 'SUPABASE_JWT_SECRET', None)

        # Use publishable key if available, otherwise fall back to anon key
        client_key = self.publishable_key if self.publishable_key else self.anon_key
        if not client_key:
            raise ValueError("Either SUPABASE_PUBLISHABLE_KEY or SUPABASE_ANON_KEY must be configured")

        self.client = create_client(self.supabase_url, client_key)
        self.key_type = 'publishable' if self.publishable_key else 'legacy'

        logger.info(f"PDF Security initialized with {self.key_type} key type")
    
    def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """
        Validate Supabase JWT token and extract user info
        
        Args:
            token: JWT token from Authorization header
            
        Returns:
            dict: User information if valid, None if invalid
        """
        try:
            if not token:
                return None

            # Remove "Bearer " prefix if present
            if token.startswith('Bearer '):
                token = token[7:]

            # Check if JWT secret is available for local validation
            if not self.jwt_secret:
                logger.warning('JWT secret not configured, cannot validate token locally')
                return None

            # Decode JWT token
            payload = jwt.decode(
                token,
                self.jwt_secret,
                algorithms=['HS256'],
                audience='authenticated'
            )
            
            # Validate token expiration
            if payload.get('exp', 0) < timezone.now().timestamp():
                logger.warning('JWT token expired')
                return None
            
            # Extract user information
            user_info = {
                'user_id': payload.get('sub'),
                'email': payload.get('email'),
                'role': payload.get('role', 'authenticated'),
                'exp': payload.get('exp'),
                'iat': payload.get('iat')
            }
            
            if not user_info['user_id']:
                logger.warning('JWT token missing user ID')
                return None
            
            return user_info
            
        except jwt.ExpiredSignatureError:
            logger.warning('JWT token expired')
            return None
        except jwt.InvalidTokenError as e:
            logger.warning(f'Invalid JWT token: {str(e)}')
            return None
        except Exception as e:
            logger.error(f'JWT validation error: {str(e)}')
            return None
    
    def get_user_from_request(self, request: HttpRequest) -> Optional[Dict[str, Any]]:
        """
        Extract and validate user from request
        
        Args:
            request: Django HttpRequest object
            
        Returns:
            dict: User information if authenticated, None otherwise
        """
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header:
            return None
        
        return self.validate_token(auth_header)


class PDFRateLimiter:
    """
    Rate limiter for PDF generation (5 PDFs per hour per user)
    """
    
    def __init__(self):
        self.rate_limit = getattr(settings, 'PDF_RATE_LIMIT', 5)  # 5 PDFs per hour
        self.time_window = getattr(settings, 'PDF_RATE_WINDOW', 3600)  # 1 hour in seconds
        self.use_cache = getattr(settings, 'USE_REDIS_CACHE', True)
    
    def check_rate_limit(self, user_id: str, ip_address: str = None) -> Tuple[bool, int]:
        """
        Check if user has exceeded rate limit
        
        Args:
            user_id: User UUID
            ip_address: Optional IP address for additional limiting
            
        Returns:
            tuple: (is_allowed, seconds_until_reset)
        """
        try:
            current_time = timezone.now()
            window_start = current_time - timedelta(seconds=self.time_window)
            
            if self.use_cache:
                # Use Redis cache for faster lookups
                return self._check_rate_limit_cached(user_id, current_time, window_start)
            else:
                # Use database for persistent tracking
                return self._check_rate_limit_db(user_id, current_time, window_start)
            
        except Exception as e:
            logger.error(f'Rate limit check failed: {str(e)}')
            # Fail open - allow request if rate limiting fails
            return True, 0
    
    def _check_rate_limit_cached(self, user_id: str, current_time: datetime, 
                                window_start: datetime) -> Tuple[bool, int]:
        """
        Check rate limit using Redis cache
        """
        cache_key = f'pdf_rate_limit:{user_id}'
        
        # Get current request count
        request_count = cache.get(cache_key, 0)
        
        if request_count >= self.rate_limit:
            # Get time until window resets
            ttl = cache.ttl(cache_key)
            seconds_until_reset = max(ttl, 0) if ttl > 0 else 0
            return False, seconds_until_reset
        
        return True, 0
    
    def _check_rate_limit_db(self, user_id: str, current_time: datetime, 
                           window_start: datetime) -> Tuple[bool, int]:
        """
        Check rate limit using database
        """
        # Count requests in current window
        request_count = RateLimitLog.objects.filter(
            user_id=user_id,
            action='pdf_generation',
            created_at__gte=window_start
        ).count()
        
        if request_count >= self.rate_limit:
            # Calculate seconds until oldest request expires
            oldest_request = RateLimitLog.objects.filter(
                user_id=user_id,
                action='pdf_generation',
                created_at__gte=window_start
            ).order_by('created_at').first()
            
            if oldest_request:
                reset_time = oldest_request.created_at + timedelta(seconds=self.time_window)
                seconds_until_reset = max((reset_time - current_time).total_seconds(), 0)
            else:
                seconds_until_reset = 0
            
            return False, int(seconds_until_reset)
        
        return True, 0
    
    def record_request(self, user_id: str, ip_address: str = None, 
                      user_agent: str = None) -> None:
        """
        Record a PDF generation request for rate limiting
        
        Args:
            user_id: User UUID
            ip_address: Client IP address
            user_agent: Client user agent
        """
        try:
            if self.use_cache:
                self._record_request_cached(user_id)
            else:
                self._record_request_db(user_id, ip_address, user_agent)
                
        except Exception as e:
            logger.error(f'Failed to record rate limit request: {str(e)}')
    
    def _record_request_cached(self, user_id: str) -> None:
        """
        Record request in Redis cache
        """
        cache_key = f'pdf_rate_limit:{user_id}'
        
        # Increment counter with expiration
        current_count = cache.get(cache_key, 0)
        cache.set(cache_key, current_count + 1, timeout=self.time_window)
    
    def _record_request_db(self, user_id: str, ip_address: str = None, 
                          user_agent: str = None) -> None:
        """
        Record request in database
        """
        RateLimitLog.objects.create(
            user_id=user_id,
            action='pdf_generation',
            ip_address=ip_address,
            user_agent=user_agent or ''
        )
    
    def get_user_stats(self, user_id: str) -> Dict[str, Any]:
        """
        Get rate limiting stats for a user
        
        Args:
            user_id: User UUID
            
        Returns:
            dict: User rate limiting statistics
        """
        try:
            current_time = timezone.now()
            window_start = current_time - timedelta(seconds=self.time_window)
            
            if self.use_cache:
                cache_key = f'pdf_rate_limit:{user_id}'
                current_count = cache.get(cache_key, 0)
                remaining = max(self.rate_limit - current_count, 0)
                ttl = cache.ttl(cache_key)
                reset_time = current_time + timedelta(seconds=max(ttl, 0)) if ttl > 0 else None
            else:
                current_count = RateLimitLog.objects.filter(
                    user_id=user_id,
                    action='pdf_generation',
                    created_at__gte=window_start
                ).count()
                remaining = max(self.rate_limit - current_count, 0)
                
                oldest_request = RateLimitLog.objects.filter(
                    user_id=user_id,
                    action='pdf_generation',
                    created_at__gte=window_start
                ).order_by('created_at').first()
                
                reset_time = None
                if oldest_request:
                    reset_time = oldest_request.created_at + timedelta(seconds=self.time_window)
            
            return {
                'limit': self.rate_limit,
                'used': current_count,
                'remaining': remaining,
                'reset_time': reset_time.isoformat() if reset_time else None,
                'window_seconds': self.time_window
            }
            
        except Exception as e:
            logger.error(f'Failed to get user rate limit stats: {str(e)}')
            return {
                'limit': self.rate_limit,
                'used': 0,
                'remaining': self.rate_limit,
                'reset_time': None,
                'window_seconds': self.time_window,
                'error': str(e)
            }


class CVDataValidator:
    """
    Validator for CV data to ensure security and data integrity
    """
    
    REQUIRED_FIELDS = ['personal_info']
    MAX_TEXT_LENGTH = 5000
    MAX_ARRAY_LENGTH = 20
    MAX_URL_LENGTH = 500
    
    @classmethod
    def validate_cv_data(cls, cv_data: Dict[str, Any]) -> None:
        """
        Validate CV data structure and content
        
        Args:
            cv_data: CV data dictionary
            
        Raises:
            InvalidCVDataError: If validation fails
        """
        if not isinstance(cv_data, dict):
            raise InvalidCVDataError("CV data must be a dictionary")
        
        # Check required fields
        for field in cls.REQUIRED_FIELDS:
            if field not in cv_data:
                raise InvalidCVDataError(f"Missing required field: {field}")
        
        # Validate personal info
        cls._validate_personal_info(cv_data.get('personal_info', {}))
        
        # Validate optional sections
        if 'work_experience' in cv_data:
            cls._validate_work_experience(cv_data['work_experience'])
        
        if 'education' in cv_data:
            cls._validate_education(cv_data['education'])
        
        if 'skills' in cv_data:
            cls._validate_skills(cv_data['skills'])
        
        if 'projects' in cv_data:
            cls._validate_projects(cv_data['projects'])
        
        # Validate text content for potential security issues
        cls._validate_content_security(cv_data)
    
    @classmethod
    def _validate_personal_info(cls, personal_info: Dict[str, Any]) -> None:
        """Validate personal information section"""
        if not isinstance(personal_info, dict):
            raise InvalidCVDataError("Personal info must be a dictionary")
        
        # Validate text fields
        for field in ['first_name', 'last_name', 'email', 'phone', 'location']:
            if field in personal_info:
                value = personal_info[field]
                if not isinstance(value, str):
                    raise InvalidCVDataError(f"Personal info {field} must be a string")
                if len(value) > cls.MAX_TEXT_LENGTH:
                    raise InvalidCVDataError(f"Personal info {field} too long")
        
        # Validate email format
        if 'email' in personal_info and personal_info['email']:
            email = personal_info['email']
            if '@' not in email or len(email) > 254:
                raise InvalidCVDataError("Invalid email address")
        
        # Validate URLs
        for url_field in ['website', 'linkedin', 'github']:
            if url_field in personal_info and personal_info[url_field]:
                url = personal_info[url_field]
                if not isinstance(url, str) or len(url) > cls.MAX_URL_LENGTH:
                    raise InvalidCVDataError(f"Invalid {url_field} URL")
    
    @classmethod
    def _validate_work_experience(cls, work_experience: list) -> None:
        """Validate work experience section"""
        if not isinstance(work_experience, list):
            raise InvalidCVDataError("Work experience must be a list")
        
        if len(work_experience) > cls.MAX_ARRAY_LENGTH:
            raise InvalidCVDataError("Too many work experience entries")
        
        for i, entry in enumerate(work_experience):
            if not isinstance(entry, dict):
                raise InvalidCVDataError(f"Work experience entry {i} must be a dictionary")
            
            for field in ['position', 'company', 'description']:
                if field in entry and entry[field]:
                    if not isinstance(entry[field], str):
                        raise InvalidCVDataError(f"Work experience {field} must be a string")
                    if len(entry[field]) > cls.MAX_TEXT_LENGTH:
                        raise InvalidCVDataError(f"Work experience {field} too long")
    
    @classmethod
    def _validate_education(cls, education: list) -> None:
        """Validate education section"""
        if not isinstance(education, list):
            raise InvalidCVDataError("Education must be a list")
        
        if len(education) > cls.MAX_ARRAY_LENGTH:
            raise InvalidCVDataError("Too many education entries")
        
        for i, entry in enumerate(education):
            if not isinstance(entry, dict):
                raise InvalidCVDataError(f"Education entry {i} must be a dictionary")
    
    @classmethod
    def _validate_skills(cls, skills: list) -> None:
        """Validate skills section"""
        if not isinstance(skills, list):
            raise InvalidCVDataError("Skills must be a list")
        
        if len(skills) > cls.MAX_ARRAY_LENGTH:
            raise InvalidCVDataError("Too many skill entries")
    
    @classmethod
    def _validate_projects(cls, projects: list) -> None:
        """Validate projects section"""
        if not isinstance(projects, list):
            raise InvalidCVDataError("Projects must be a list")
        
        if len(projects) > cls.MAX_ARRAY_LENGTH:
            raise InvalidCVDataError("Too many project entries")
    
    @classmethod
    def _validate_content_security(cls, cv_data: Dict[str, Any]) -> None:
        """
        Validate content for potential security issues
        Check for suspicious patterns, scripts, etc.
        """
        def check_text_content(text: str, field_name: str) -> None:
            if not isinstance(text, str):
                return
            
            # Check for potential script injection
            suspicious_patterns = [
                '<script', '</script>', 'javascript:', 'vbscript:',
                r'on\w+\s*=', r'eval\s*\(', r'expression\s*\(',
                'data:text/html', 'data:application/'
            ]
            
            text_lower = text.lower()
            for pattern in suspicious_patterns:
                if pattern in text_lower:
                    logger.warning(f'Suspicious content detected in {field_name}: {pattern}')
                    raise InvalidCVDataError(f"Potentially unsafe content in {field_name}")
        
        # Recursively check all string values
        def recursive_check(obj: Any, path: str = '') -> None:
            if isinstance(obj, dict):
                for key, value in obj.items():
                    recursive_check(value, f"{path}.{key}" if path else key)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    recursive_check(item, f"{path}[{i}]")
            elif isinstance(obj, str):
                check_text_content(obj, path)
        
        recursive_check(cv_data)
    
    @classmethod
    def sanitize_cv_data(cls, cv_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize CV data by removing/escaping potentially dangerous content
        
        Args:
            cv_data: Raw CV data
            
        Returns:
            dict: Sanitized CV data
        """
        import html
        import re
        
        def sanitize_text(text: str) -> str:
            if not isinstance(text, str):
                return text
            
            # Remove potential script tags and dangerous attributes
            text = re.sub(r'<script[^>]*>.*?</script>', '', text, flags=re.IGNORECASE | re.DOTALL)
            text = re.sub(r'on\w+\s*=\s*["\'][^"\']*["\']', '', text, flags=re.IGNORECASE)
            text = re.sub(r'javascript:', '', text, flags=re.IGNORECASE)
            text = re.sub(r'vbscript:', '', text, flags=re.IGNORECASE)
            
            # HTML escape
            text = html.escape(text)
            
            return text
        
        def recursive_sanitize(obj: Any) -> Any:
            if isinstance(obj, dict):
                return {key: recursive_sanitize(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [recursive_sanitize(item) for item in obj]
            elif isinstance(obj, str):
                return sanitize_text(obj)
            else:
                return obj
        
        return recursive_sanitize(cv_data)