"""
Consolidated Celery tasks for async PDF generation

This module provides comprehensive asynchronous PDF generation tasks using Celery,
including progress tracking, error recovery, file storage integration, cleanup tasks,
and analytics reporting. Consolidates functionality from both tasks.py and async_tasks.py.
"""

import logging
import traceback
import time
import os
import tempfile
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from django.utils import timezone
from django.conf import settings
from django.db import models
from django.contrib.auth.models import User
from celery import shared_task, current_task
from celery.exceptions import Retry, WorkerLostError, MaxRetriesExceededError

from .weasyprint_service import WeasyPrintService
from .storage_service import SupabaseStorageService
from .exceptions import (
    PDFGenerationError,
    PDFTimeoutError,
    PDFMemoryError,
    StorageError
)
from apps.cv_builder.models import PDFGenerationLog, CVProfile

logger = logging.getLogger('cvflo.tasks')


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def generate_pdf_async(
    self,
    generation_id: str,
    cv_data: Dict[str, Any],
    visibility: Dict[str, bool],
    template_name: str,
    user_id: str,
    options: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Async task for PDF generation with comprehensive error handling
    
    Args:
        generation_id: UUID of PDFGenerationLog record
        cv_data: CV data dictionary
        visibility: Section visibility settings
        template_name: Template identifier
        user_id: User UUID
        options: Additional PDF generation options
        
    Returns:
        Dict containing task result information
        
    Raises:
        Retry: If task should be retried
    """
    start_time = timezone.now()
    
    try:
        # Update task status to processing
        generation = PDFGenerationLog.objects.get(id=generation_id)
        generation.status = 'processing'
        generation.task_id = self.request.id
        generation.processing_started_at = start_time
        generation.save()
        
        logger.info(
            f"Starting PDF generation task: {self.request.id}, "
            f"generation_id: {generation_id}, user: {user_id}"
        )
        
        # Update task progress
        self.update_state(
            state='PROGRESS',
            meta={'stage': 'initializing', 'progress': 10}
        )
        
        # Initialize services
        weasyprint_service = WeasyPrintService()
        storage_service = SupabaseStorageService()
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'stage': 'generating_pdf', 'progress': 30}
        )
        
        # Generate PDF
        try:
            pdf_bytes = weasyprint_service.generate_pdf(
                cv_data=cv_data,
                visibility=visibility,
                template_name=template_name,
                options=options or {}
            )
        except PDFTimeoutError as e:
            logger.error(f"PDF generation timed out: {e}")
            return _handle_task_failure(generation, str(e), 'timeout')
        except PDFMemoryError as e:
            logger.error(f"PDF generation memory error: {e}")
            return _handle_task_failure(generation, str(e), 'memory_error')
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            return _handle_task_failure(generation, str(e), 'generation_error')
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'stage': 'uploading_file', 'progress': 70}
        )
        
        # Generate filename
        filename = weasyprint_service.get_suggested_filename(cv_data)
        
        # Upload to storage
        try:
            file_url, file_path = storage_service.upload_pdf(
                pdf_bytes=pdf_bytes,
                filename=filename,
                user_id=user_id,
                generation_id=generation_id
            )
        except StorageError as e:
            logger.error(f"File upload failed: {e}")
            return _handle_task_failure(generation, str(e), 'storage_error')
        
        # Update progress
        self.update_state(
            state='PROGRESS',
            meta={'stage': 'finalizing', 'progress': 90}
        )
        
        # Update generation record with success
        completion_time = timezone.now()
        generation.status = 'completed'
        generation.file_url = file_url
        generation.file_path = file_path
        generation.file_size = len(pdf_bytes)
        generation.processing_completed_at = completion_time
        generation.processing_duration = completion_time - start_time
        generation.save()
        
        result = {
            'status': 'completed',
            'generation_id': generation_id,
            'file_url': file_url,
            'file_size': len(pdf_bytes),
            'processing_duration': (completion_time - start_time).total_seconds(),
            'template_name': template_name,
        }
        
        logger.info(
            f"PDF generation completed successfully: {self.request.id}, "
            f"size: {len(pdf_bytes)} bytes, duration: {result['processing_duration']:.2f}s"
        )
        
        return result
        
    except PDFGenerationLog.DoesNotExist:
        error_msg = f"PDFGenerationLog with id {generation_id} not found"
        logger.error(error_msg)
        return {'status': 'failed', 'error': error_msg}
        
    except Exception as exc:
        # Handle unexpected errors with retry logic
        logger.error(
            f"Unexpected error in PDF generation task {self.request.id}: {exc}\n"
            f"Traceback: {traceback.format_exc()}"
        )
        
        # Update generation record with error
        try:
            generation = PDFGenerationLog.objects.get(id=generation_id)
            generation.status = 'failed'
            generation.error_message = str(exc)
            generation.retry_count = self.request.retries
            generation.save()
        except Exception:
            pass
        
        # Retry if we haven't exceeded max retries
        if self.request.retries < self.max_retries:
            logger.info(f"Retrying task {self.request.id}, attempt {self.request.retries + 1}")
            raise self.retry(countdown=60 * (2 ** self.request.retries), exc=exc)
        else:
            return {
                'status': 'failed',
                'error': str(exc),
                'retries_exhausted': True
            }


@shared_task(bind=True, max_retries=2)
def cleanup_expired_pdfs(self):
    """
    Cleanup expired PDF files and generation records
    
    This task runs periodically to clean up old PDF files and generation records
    to manage storage costs and maintain system performance.
    """
    try:
        logger.info("Starting cleanup of expired PDFs")
        
        # Get expiration time (default: 24 hours)
        expiration_hours = getattr(settings, 'PDF_EXPIRATION_HOURS', 24)
        expiration_time = timezone.now() - timedelta(hours=expiration_hours)
        
        # Find expired generations
        expired_generations = PDFGenerationLog.objects.filter(
            created_at__lt=expiration_time,
            status='completed'
        )
        
        cleanup_count = 0
        storage_service = SupabaseStorageService()
        
        for generation in expired_generations:
            try:
                # Delete file from storage
                if generation.file_path:
                    storage_service.delete_file(generation.file_path)
                
                # Update generation record
                generation.status = 'expired'
                generation.file_url = None
                generation.file_path = None
                generation.expires_at = timezone.now()
                generation.save()
                
                cleanup_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to cleanup generation {generation.id}: {e}")
        
        logger.info(f"Cleanup completed: {cleanup_count} PDFs expired")
        
        return {
            'status': 'completed',
            'cleaned_up': cleanup_count,
            'expiration_time': expiration_time.isoformat()
        }
        
    except Exception as exc:
        logger.error(f"PDF cleanup task failed: {exc}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=300, exc=exc)  # Retry in 5 minutes
        else:
            return {'status': 'failed', 'error': str(exc)}


@shared_task(bind=True)
def health_check_pdf_service(self):
    """
    Health check task for PDF generation service
    
    Verifies that WeasyPrint service is available and functional
    """
    try:
        logger.info("Running PDF service health check")
        
        weasyprint_service = WeasyPrintService()
        health_status = weasyprint_service.check_service_health()
        
        logger.info(f"PDF service health check completed: {health_status['status']}")
        
        return health_status
        
    except Exception as exc:
        logger.error(f"PDF service health check failed: {exc}")
        return {
            'status': 'unhealthy',
            'error': str(exc),
            'timestamp': timezone.now().isoformat()
        }


@shared_task(bind=True, max_retries=3)
def batch_generate_pdfs(
    self,
    generation_requests: list,
    user_id: str,
    batch_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Batch PDF generation task for multiple CVs
    
    Args:
        generation_requests: List of generation request dictionaries
        user_id: User UUID
        batch_id: Optional batch identifier
        
    Returns:
        Dictionary with batch results
    """
    try:
        logger.info(
            f"Starting batch PDF generation: {len(generation_requests)} requests, "
            f"user: {user_id}, batch: {batch_id}"
        )
        
        results = []
        successful = 0
        failed = 0
        
        for i, request in enumerate(generation_requests):
            try:
                # Update progress
                progress = int((i / len(generation_requests)) * 100)
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'batch_id': batch_id,
                        'progress': progress,
                        'completed': i,
                        'total': len(generation_requests)
                    }
                )
                
                # Generate PDF for this request
                result = generate_pdf_async.delay(
                    generation_id=request['generation_id'],
                    cv_data=request['cv_data'],
                    visibility=request['visibility'],
                    template_name=request['template_name'],
                    user_id=user_id,
                    options=request.get('options')
                ).get(timeout=300)  # 5 minute timeout per PDF
                
                results.append(result)
                
                if result.get('status') == 'completed':
                    successful += 1
                else:
                    failed += 1
                    
            except Exception as e:
                logger.error(f"Batch generation failed for request {i}: {e}")
                results.append({
                    'status': 'failed',
                    'error': str(e),
                    'generation_id': request.get('generation_id')
                })
                failed += 1
        
        batch_result = {
            'status': 'completed',
            'batch_id': batch_id,
            'total': len(generation_requests),
            'successful': successful,
            'failed': failed,
            'results': results
        }
        
        logger.info(
            f"Batch PDF generation completed: {successful} successful, {failed} failed"
        )
        
        return batch_result
        
    except Exception as exc:
        logger.error(f"Batch PDF generation failed: {exc}")
        
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=120, exc=exc)
        else:
            return {
                'status': 'failed',
                'error': str(exc),
                'batch_id': batch_id
            }


def _handle_task_failure(
    generation: PDFGenerationLog,
    error_message: str,
    error_type: str
) -> Dict[str, Any]:
    """
    Handle task failure by updating generation record
    
    Args:
        generation: PDFGenerationLog instance
        error_message: Error message
        error_type: Type of error
        
    Returns:
        Dictionary with failure information
    """
    try:
        generation.status = 'failed'
        generation.error_message = error_message
        generation.processing_completed_at = timezone.now()
        generation.metadata = generation.metadata or {}
        generation.metadata['error_type'] = error_type
        generation.save()
    except Exception as e:
        logger.error(f"Failed to update generation record: {e}")
    
    return {
        'status': 'failed',
        'error': error_message,
        'error_type': error_type,
        'generation_id': str(generation.id)
    }


# Puppeteer-based PDF generation removed - using WeasyPrint only
# All PDF generation now handled by generate_pdf_async() using WeasyPrintService


@shared_task
def retry_failed_generations():
    """
    Retry failed PDF generations that can be retried
    Should be run periodically (e.g., every 30 minutes)
    """
    logger.info("Starting retry task for failed PDF generations")

    try:
        # Find failed generations that can be retried and are not too old
        cutoff_time = timezone.now() - timedelta(hours=24)  # Don't retry if older than 24 hours

        failed_generations = PDFGenerationLog.objects.filter(
            status='failed',
            retry_count__lt=models.F('max_retries'),
            created_at__gte=cutoff_time
        )

        retry_count = 0

        for generation in failed_generations:
            try:
                # Get CV data
                cv_profile = generation.cv_profile
                visibility = cv_profile.cv_content.get('visibility', {})

                # Restart the generation task
                task = generate_pdf_async.delay(
                    str(generation.id),
                    cv_profile.cv_content,
                    visibility,
                    generation.template_name
                )

                # Update task ID
                generation.task_id = task.id
                generation.status = 'pending'
                generation.save()

                retry_count += 1
                logger.info(f"Retrying PDF generation {generation.id}")

            except Exception as e:
                logger.error(f"Failed to retry PDF generation {generation.id}: {str(e)}")

        logger.info(f"Retry task completed: {retry_count} generations retried")

        return {
            'retried': retry_count
        }

    except Exception as e:
        logger.error(f"Retry task failed: {str(e)}")
        raise


@shared_task
def generate_analytics_report():
    """
    Generate analytics report for PDF generation performance
    Should be run daily
    """
    logger.info("Starting PDF generation analytics report")

    try:
        from django.db.models import Count, Avg, Q

        # Last 24 hours
        last_24h = timezone.now() - timedelta(hours=24)

        # Query statistics
        stats = PDFGenerationLog.objects.filter(created_at__gte=last_24h).aggregate(
            total_requests=Count('id'),
            completed=Count('id', filter=Q(status='completed')),
            failed=Count('id', filter=Q(status='failed')),
            processing=Count('id', filter=Q(status__in=['pending', 'processing'])),
            avg_processing_time=Avg('processing_duration'),
            avg_file_size=Avg('file_size')
        )

        # Template breakdown
        template_stats = PDFGenerationLog.objects.filter(
            created_at__gte=last_24h
        ).values('template_name').annotate(
            count=Count('id')
        ).order_by('-count')

        # User activity
        user_stats = PDFGenerationLog.objects.filter(
            created_at__gte=last_24h
        ).values('user_id').annotate(
            count=Count('id')
        ).count()  # Unique users

        report = {
            'period': '24 hours',
            'timestamp': timezone.now().isoformat(),
            'totals': stats,
            'templates': list(template_stats),
            'unique_users': user_stats,
            'success_rate': (stats['completed'] / max(stats['total_requests'], 1)) * 100 if stats['total_requests'] else 0
        }

        logger.info(f"Analytics report generated: {report}")

        return report

    except Exception as e:
        logger.error(f"Analytics report generation failed: {str(e)}")
        raise