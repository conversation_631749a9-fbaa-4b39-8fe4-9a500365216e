"""
Custom exceptions for PDF generation system
"""


class PDFGenerationError(Exception):
    """
    Base exception for PDF generation errors
    """
    def __init__(self, message: str, code: str = None):
        self.message = message
        self.code = code or 'PDF_GENERATION_ERROR'
        super().__init__(self.message)


class PDFServiceUnavailableError(PDFGenerationError):
    """
    Exception raised when PDF generation service is unavailable
    """
    def __init__(self, message: str = "PDF generation service is unavailable"):
        super().__init__(message, 'PDF_SERVICE_UNAVAILABLE')


class TemplateNotFoundError(PDFGenerationError):
    """
    Exception raised when requested template is not found
    """
    def __init__(self, template_name: str):
        message = f"Template '{template_name}' not found"
        super().__init__(message, 'TEMPLATE_NOT_FOUND')


class InvalidCVDataError(PDFGenerationError):
    """
    Exception raised when CV data is invalid or incomplete
    """
    def __init__(self, message: str = "Invalid CV data"):
        super().__init__(message, 'INVALID_CV_DATA')


class RateLimitExceededError(PDFGenerationError):
    """
    Exception raised when user exceeds rate limit
    """
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = None):
        self.retry_after = retry_after
        super().__init__(message, 'RATE_LIMIT_EXCEEDED')


class StorageError(PDFGenerationError):
    """
    Exception raised when file storage operations fail
    """
    def __init__(self, message: str = "Storage operation failed"):
        super().__init__(message, 'STORAGE_ERROR')


class PDFTimeoutError(PDFGenerationError):
    """
    Exception raised when PDF generation times out
    """
    def __init__(self, message: str = "PDF generation timed out", timeout: int = None):
        self.timeout = timeout
        super().__init__(message, 'PDF_TIMEOUT')


class PDFMemoryError(PDFGenerationError):
    """
    Exception raised when PDF generation exceeds memory limits
    """
    def __init__(self, message: str = "PDF generation exceeded memory limit", memory_used: int = None):
        self.memory_used = memory_used
        super().__init__(message, 'PDF_MEMORY_ERROR')