"""
Production-ready WeasyPrint service for CV PDF generation

This service provides a robust, memory-efficient implementation for generating
PDFs from CV data using WeasyPrint with comprehensive error handling,
template management, and performance optimization.
"""

import logging
import io
import os
import gc
import tempfile
import threading
from typing import Dict, Any, Optional, Tuple, List
from datetime import datetime, timedelta
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
import weakref

from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from django.core.cache import cache

try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
    WEASYPRINT_IMPORT_ERROR = None
except (ImportError, OSError) as e:
    WEASYPRINT_AVAILABLE = False
    WEASYPRINT_IMPORT_ERROR = str(e)
    # Create dummy classes to prevent import errors
    HTML = None
    CSS = None
    FontConfiguration = None

from .exceptions import (
    PDFGenerationError, 
    TemplateNotFoundError, 
    PDFServiceUnavailableError,
    PDFTimeoutError,
    PDFMemoryError
)
from .template_manager import TemplateManager

logger = logging.getLogger('cvflo.weasyprint')


class WeasyPrintPools:
    """
    Thread pool management for WeasyPrint PDF generation
    
    Manages concurrent PDF generation with resource limits and cleanup
    """
    
    def __init__(self, max_workers: int = 3, timeout: int = 60):
        self.max_workers = max_workers
        self.timeout = timeout
        self._executor = None
        self._lock = threading.Lock()
        
    def get_executor(self) -> ThreadPoolExecutor:
        """Get or create thread pool executor"""
        if self._executor is None or self._executor._shutdown:
            with self._lock:
                if self._executor is None or self._executor._shutdown:
                    self._executor = ThreadPoolExecutor(
                        max_workers=self.max_workers,
                        thread_name_prefix='weasyprint-'
                    )
        return self._executor
    
    def shutdown(self):
        """Shutdown thread pool"""
        if self._executor:
            self._executor.shutdown(wait=True)


class FontManager:
    """
    Font configuration and management for WeasyPrint
    
    Handles font loading, caching, and optimization for consistent PDF rendering
    """
    
    def __init__(self):
        self._font_config = None
        self._font_cache = {}
        self._lock = threading.Lock()
        
    def get_font_config(self) -> FontConfiguration:
        """Get or create font configuration"""
        if self._font_config is None:
            with self._lock:
                if self._font_config is None:
                    self._font_config = FontConfiguration()
                    self._preload_system_fonts()
        return self._font_config
    
    def _preload_system_fonts(self):
        """Preload commonly used system fonts"""
        try:
            common_fonts = [
                'Arial', 'Helvetica', 'Times New Roman', 'Georgia',
                'Verdana', 'Tahoma', 'Calibri', 'Open Sans'
            ]
            
            for font_name in common_fonts:
                try:
                    # This triggers font loading in WeasyPrint
                    self._font_config.get_font_size(font_name, 'normal', 'normal', 12)
                except Exception:
                    # Font not available, continue
                    pass
                    
        except Exception as e:
            logger.warning(f"Font preloading failed: {e}")


class MemoryManager:
    """
    Memory management for WeasyPrint operations
    
    Monitors and controls memory usage during PDF generation
    """
    
    def __init__(self, max_memory_mb: int = 512):
        self.max_memory_mb = max_memory_mb
        self._active_generations = weakref.WeakSet()
        
    def check_memory_usage(self):
        """Check current memory usage"""
        try:
            import psutil
            process = psutil.Process()
            memory_mb = process.memory_info().rss / 1024 / 1024
            
            if memory_mb > self.max_memory_mb:
                logger.warning(f"High memory usage: {memory_mb:.1f}MB")
                self._force_cleanup()
                
            return memory_mb
        except ImportError:
            # psutil not available, skip memory monitoring
            return 0
    
    def _force_cleanup(self):
        """Force garbage collection and cleanup"""
        gc.collect()
        logger.info("Forced memory cleanup completed")


class WeasyPrintService:
    """
    Consolidated PDF generation service for CV PDFs

    Features:
    - Memory-efficient PDF generation with resource pooling
    - Comprehensive error handling and recovery
    - Template management with caching
    - Font optimization and preloading
    - Concurrent generation with timeout protection
    - Performance monitoring and logging
    - Enhanced features from all previous service implementations
    """

    def __init__(self):
        # Check WeasyPrint availability
        if not WEASYPRINT_AVAILABLE:
            raise PDFServiceUnavailableError(
                f"WeasyPrint is not available: {WEASYPRINT_IMPORT_ERROR}"
            )

        # Initialize components
        self.pools = WeasyPrintPools(
            max_workers=getattr(settings, 'WEASYPRINT_MAX_WORKERS', 3),
            timeout=getattr(settings, 'WEASYPRINT_TIMEOUT', 60)
        )
        self.font_manager = FontManager()
        self.memory_manager = MemoryManager(
            max_memory_mb=getattr(settings, 'WEASYPRINT_MAX_MEMORY_MB', 512)
        )
        self._template_manager = None  # Lazy initialization

        # Configuration
        self.base_url = getattr(settings, 'STATIC_URL', '/static/')
        self.cache_timeout = getattr(settings, 'WEASYPRINT_CACHE_TIMEOUT', 300)  # 5 minutes
        self.templates_dir = getattr(settings, 'PDF_GENERATION', {}).get('TEMPLATES_DIR', 'templates/cv')

        logger.info("Consolidated WeasyPrint service initialized successfully")

    @property
    def template_manager(self):
        """Lazy initialization of template manager"""
        if self._template_manager is None:
            self._template_manager = TemplateManager()
        return self._template_manager
    
    def generate_pdf(
        self,
        cv_data: Dict[str, Any],
        visibility: Dict[str, bool],
        template_name: str,
        options: Optional[Dict[str, Any]] = None
    ) -> bytes:
        """
        Generate PDF from CV data using WeasyPrint
        
        Args:
            cv_data: CV data dictionary
            visibility: Section visibility settings
            template_name: Template identifier
            options: Additional PDF generation options
            
        Returns:
            bytes: PDF content as bytes
            
        Raises:
            PDFGenerationError: If PDF generation fails
            TemplateNotFoundError: If template is not found
            PDFTimeoutError: If generation times out
            PDFMemoryError: If memory limit is exceeded
        """
        start_time = timezone.now()
        
        try:
            # Validate inputs
            self._validate_inputs(cv_data, template_name)
            
            # Check memory before starting
            memory_before = self.memory_manager.check_memory_usage()
            
            # Generate HTML content
            html_content = self._generate_html(cv_data, visibility, template_name)
            
            # Get CSS styles
            css_content = self._get_template_css(template_name)
            
            # Generate PDF using thread pool
            pdf_bytes = self._generate_pdf_threaded(
                html_content, 
                css_content, 
                options or {}
            )
            
            # Log performance metrics
            duration = (timezone.now() - start_time).total_seconds()
            memory_after = self.memory_manager.check_memory_usage()
            
            logger.info(
                f"PDF generated successfully: template={template_name}, "
                f"duration={duration:.2f}s, size={len(pdf_bytes)}bytes, "
                f"memory_delta={memory_after - memory_before:.1f}MB"
            )
            
            return pdf_bytes
            
        except Exception as e:
            duration = (timezone.now() - start_time).total_seconds()
            logger.error(
                f"PDF generation failed: template={template_name}, "
                f"duration={duration:.2f}s, error={str(e)}"
            )
            
            # Clean up and re-raise with appropriate error type
            self._cleanup_after_error()
            
            if isinstance(e, (PDFGenerationError, TemplateNotFoundError, 
                            PDFTimeoutError, PDFMemoryError)):
                raise
            else:
                raise PDFGenerationError(f"Unexpected error during PDF generation: {str(e)}")
    
    def generate_pdf_from_html(
        self,
        html_content: str,
        css_content: str = "",
        options: Optional[Dict[str, Any]] = None
    ) -> bytes:
        """
        Generate PDF directly from HTML content
        
        Args:
            html_content: HTML content string
            css_content: CSS styles string
            options: Additional PDF generation options
            
        Returns:
            bytes: PDF content as bytes
        """
        try:
            return self._generate_pdf_threaded(html_content, css_content, options or {})
            
        except Exception as e:
            logger.error(f"PDF generation from HTML failed: {str(e)}")
            self._cleanup_after_error()
            raise PDFGenerationError(f"Failed to generate PDF from HTML: {str(e)}")
    
    def _validate_inputs(self, cv_data: Dict[str, Any], template_name: str):
        """Validate input parameters"""
        if not isinstance(cv_data, dict):
            raise PDFGenerationError("CV data must be a dictionary")
        
        if not template_name or not isinstance(template_name, str):
            raise PDFGenerationError("Template name must be a non-empty string")
        
        if not self.template_manager.template_exists(template_name):
            raise TemplateNotFoundError(f"Template '{template_name}' not found")
    
    def _generate_html(
        self,
        cv_data: Dict[str, Any],
        visibility: Dict[str, bool],
        template_name: str
    ) -> str:
        """Generate HTML content from CV data"""
        try:
            # Create template context
            context = {
                'cv_data': cv_data,
                'visibility': visibility,
                'template_name': template_name,
                'generation_timestamp': timezone.now(),
                'static_url': self.base_url,
            }
            
            # Apply data filtering based on visibility
            filtered_cv_data = self._filter_cv_data(cv_data, visibility)
            context['cv_data'] = filtered_cv_data
            
            # Render template
            html_content = self.template_manager.render_template(template_name, context)
            
            return html_content
            
        except Exception as e:
            logger.error(f"HTML generation failed: {str(e)}")
            raise PDFGenerationError(f"Failed to generate HTML: {str(e)}")
    
    def _get_template_css(self, template_name: str) -> str:
        """Get CSS content for template"""
        try:
            return self.template_manager.get_template_css(template_name)
        except Exception as e:
            logger.warning(f"Failed to load CSS for template {template_name}: {e}")
            return ""
    
    def _generate_pdf_threaded(
        self,
        html_content: str,
        css_content: str,
        options: Dict[str, Any]
    ) -> bytes:
        """Generate PDF using thread pool with timeout protection"""
        executor = self.pools.get_executor()
        
        future = executor.submit(
            self._generate_pdf_core,
            html_content,
            css_content,
            options
        )
        
        try:
            return future.result(timeout=self.pools.timeout)
        except FutureTimeoutError:
            logger.error(f"PDF generation timed out after {self.pools.timeout}s")
            raise PDFTimeoutError(f"PDF generation timed out after {self.pools.timeout}s")
        except Exception as e:
            logger.error(f"PDF generation failed in thread: {str(e)}")
            raise
    
    def _generate_pdf_core(
        self,
        html_content: str,
        css_content: str,
        options: Dict[str, Any]
    ) -> bytes:
        """Core PDF generation using WeasyPrint"""
        try:
            # Get font configuration
            font_config = self.font_manager.get_font_config()
            
            # Create HTML document
            html_doc = HTML(
                string=html_content,
                base_url=self.base_url,
                encoding='utf-8'
            )
            
            # Prepare CSS
            css_objects = []
            if css_content:
                css_objects.append(CSS(string=css_content, font_config=font_config))
            
            # Add default print CSS
            default_css = self._get_default_print_css()
            if default_css:
                css_objects.append(CSS(string=default_css, font_config=font_config))
            
            # Generate PDF with options
            render_options = self._prepare_render_options(options)
            
            # Create PDF in memory
            pdf_buffer = io.BytesIO()
            html_doc.write_pdf(
                pdf_buffer,
                stylesheets=css_objects,
                font_config=font_config,
                **render_options
            )
            
            pdf_bytes = pdf_buffer.getvalue()
            pdf_buffer.close()
            
            # Validate PDF size
            if len(pdf_bytes) == 0:
                raise PDFGenerationError("Generated PDF is empty")
            
            return pdf_bytes
            
        except Exception as e:
            logger.error(f"WeasyPrint core generation failed: {str(e)}")
            raise PDFGenerationError(f"WeasyPrint generation failed: {str(e)}")
    
    def _prepare_render_options(self, options: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare WeasyPrint render options"""
        render_options = {
            'presentational_hints': True,
            'optimize_size': ('fonts', 'images'),
        }
        
        # Add custom options
        if 'pdf_version' in options:
            render_options['pdf_version'] = options['pdf_version']
        
        if 'pdf_identifier' in options:
            render_options['pdf_identifier'] = options['pdf_identifier']
        
        return render_options
    
    def _get_default_print_css(self) -> str:
        """Get default CSS for print media"""
        return """
        @page {
            size: A4;
            margin: 20mm;
            @top-center {
                content: "";
            }
            @bottom-center {
                content: "";
            }
        }
        
        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        
        h1, h2, h3, h4, h5, h6 {
            page-break-after: avoid;
            margin-top: 0;
        }
        
        p, li {
            orphans: 2;
            widows: 2;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        .no-break {
            page-break-inside: avoid;
        }
        
        table {
            page-break-inside: avoid;
        }
        
        img {
            max-width: 100%;
            height: auto;
        }
        """
    
    def _filter_cv_data(
        self,
        cv_data: Dict[str, Any],
        visibility: Dict[str, bool]
    ) -> Dict[str, Any]:
        """Filter CV data based on visibility settings"""
        filtered_data = cv_data.copy()
        
        # Filter sections based on visibility
        for section, visible in visibility.items():
            if not visible and section in filtered_data:
                # Keep the section but mark it as hidden
                if isinstance(filtered_data[section], dict):
                    filtered_data[section]['_hidden'] = True
                elif isinstance(filtered_data[section], list):
                    # For arrays, we might want to hide the entire section
                    filtered_data[section] = []
        
        return filtered_data
    
    def _cleanup_after_error(self):
        """Cleanup resources after error"""
        try:
            gc.collect()
            self.memory_manager.check_memory_usage()
        except Exception as e:
            logger.warning(f"Cleanup after error failed: {e}")
    
    def get_suggested_filename(self, cv_data: Dict[str, Any]) -> str:
        """Generate suggested filename from CV data"""
        try:
            personal_info = cv_data.get('personalInfo', {})
            first_name = personal_info.get('firstName', '').strip()
            last_name = personal_info.get('lastName', '').strip()
            
            if first_name or last_name:
                name_part = f"{first_name}_{last_name}".strip('_')
                filename = f"{name_part}_CV.pdf"
            else:
                filename = "CV_Resume.pdf"
            
            # Sanitize filename
            filename = "".join(c for c in filename if c.isalnum() or c in ('_', '-', '.'))
            
            return filename
            
        except Exception:
            return "CV_Resume.pdf"
    
    def check_service_health(self) -> Dict[str, Any]:
        """Check service health and availability"""
        health = {
            'status': 'healthy',
            'weasyprint_available': WEASYPRINT_AVAILABLE,
            'timestamp': timezone.now().isoformat(),
        }
        
        if not WEASYPRINT_AVAILABLE:
            health['status'] = 'unhealthy'
            health['error'] = WEASYPRINT_IMPORT_ERROR
            return health
        
        try:
            # Test basic PDF generation
            test_html = "<html><body><h1>Test</h1></body></html>"
            test_pdf = self._generate_pdf_core(test_html, "", {})
            
            health['test_generation'] = 'success'
            health['test_pdf_size'] = len(test_pdf)
            
        except Exception as e:
            health['status'] = 'degraded'
            health['test_generation'] = 'failed'
            health['test_error'] = str(e)
        
        # Add memory information
        try:
            health['memory_usage_mb'] = self.memory_manager.check_memory_usage()
        except Exception:
            pass
        
        return health
    
    def generate_pdf_from_html(self, html_content: str, styles: str = '',
                              template_name: str = 'classic-0') -> bytes:
        """
        Generate PDF from pre-rendered HTML content
        Consolidated from services.py implementation

        Args:
            html_content: Pre-rendered HTML content
            styles: Additional CSS styles
            template_name: Template name for configuration

        Returns:
            bytes: Generated PDF as bytes
        """
        try:
            logger.info(f'Generating PDF from HTML content, template: {template_name}')

            # Combine template CSS with additional styles
            template_css = self.template_manager.get_template_css(template_name)
            combined_css = f"{template_css}\n{styles}" if template_css else styles

            # Add CSS to HTML if not already present
            if combined_css and '<style>' not in html_content:
                # Insert CSS before closing head tag or at the beginning
                if '</head>' in html_content:
                    html_content = html_content.replace(
                        '</head>',
                        f'<style>{combined_css}</style></head>'
                    )
                else:
                    html_content = f'<style>{combined_css}</style>{html_content}'

            # Convert to PDF using core generation method
            return self._generate_pdf_threaded(html_content, combined_css, {})

        except Exception as e:
            logger.error(f'PDF generation from HTML failed: {str(e)}')
            self._cleanup_after_error()
            raise PDFGenerationError(f'Failed to generate PDF from HTML: {str(e)}')

    def get_available_templates(self) -> list:
        """
        Get list of available CV templates
        Consolidated from services.py implementation

        Returns:
            list: List of available templates with their configurations
        """
        try:
            return self.template_manager.get_template_list()

        except Exception as e:
            logger.error(f'Failed to get available templates: {str(e)}')
            raise PDFGenerationError(f'Failed to get available templates: {str(e)}')

    def generate_pdf_with_cache(self, cv_data: Dict[str, Any], visibility: Dict[str, bool],
                               template_name: str = 'classic-0') -> Tuple[bytes, bool]:
        """
        Generate PDF with intelligent caching
        Enhanced feature from enhanced_services.py

        Args:
            cv_data: Dictionary containing CV data
            visibility: Dictionary containing section visibility settings
            template_name: Name of the template to use

        Returns:
            Tuple of (pdf_bytes, was_cached)
        """
        from django.core.cache import cache
        import hashlib
        import json
        import time

        # Create cache key based on content hash
        content_str = json.dumps({
            'cv_data': cv_data,
            'visibility': visibility,
            'template_name': template_name,
        }, sort_keys=True)

        hash_obj = hashlib.sha256(content_str.encode())
        cache_key = f"pdf_cache:{hash_obj.hexdigest()}"

        # Try to get from cache
        cached_pdf = cache.get(cache_key)
        if cached_pdf:
            logger.info(f'PDF served from cache: {cache_key[:8]}...')
            return cached_pdf, True

        # Generate new PDF
        start_time = time.time()
        pdf_data = self.generate_pdf(cv_data, visibility, template_name)
        generation_time = time.time() - start_time

        # Cache the PDF
        cache.set(cache_key, pdf_data, self.cache_timeout)

        logger.info(f'PDF generated and cached in {generation_time:.2f}s: {cache_key[:8]}...')
        return pdf_data, False

    def __del__(self):
        """Cleanup resources on destruction"""
        try:
            if hasattr(self, 'pools'):
                self.pools.shutdown()
        except Exception:
            pass