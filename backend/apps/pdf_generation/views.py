"""
Enhanced Views for PDF Generation app with async processing

These views provide REST API endpoints for PDF generation with security,
rate limiting, and async processing capabilities.
"""

import logging
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.http import HttpResponse, JsonResponse
from django.utils.decorators import method_decorator
from django.utils import timezone
from django_ratelimit.decorators import ratelimit
from django.conf import settings
from django.shortcuts import get_object_or_404

from .weasyprint_service import WeasyPrintService
from .template_manager import TemplateManager
from .storage_service import SupabaseStorageService
from .security import SupabaseJWTAuth, PDFRateLimiter, CVDataValidator
from .async_tasks import generate_pdf_async
from .exceptions import (
    PDFGenerationError, PDFServiceUnavailableError, RateLimitExceededError,
    InvalidCVDataError, TemplateNotFoundError
)
from apps.cv_builder.models import PDFGenerationLog, CVProfile
from apps.cv_builder.serializers import PDFGenerationSerializer, HTMLPDFGenerationSerializer

logger = logging.getLogger('cvflo.pdf_generation')


class GeneratePDFView(APIView):
    """
    Generate PDF from CV data
    Equivalent to Node.js generatePdf method
    """
    
    permission_classes = [IsAuthenticated]
    
    @method_decorator(ratelimit(key='user', rate='10/15min', method='POST'))
    def post(self, request):
        """Generate PDF from CV data"""
        try:
            logger.info(f'PDF generation requested by user: {request.user.email}')
            
            # Validate request data
            serializer = PDFGenerationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            cv_data = serializer.validated_data['cv_data']
            visibility = serializer.validated_data['visibility']
            template_name = serializer.validated_data['template_name']
            
            # Generate PDF using WeasyPrint service (lazy initialization)
            weasyprint_service = WeasyPrintService()
            pdf_bytes = weasyprint_service.generate_pdf(cv_data, visibility, template_name)
            
            # Get suggested filename
            filename = weasyprint_service.get_suggested_filename(cv_data)
            
            # Create HTTP response with PDF
            response = HttpResponse(pdf_bytes, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Content-Length'] = len(pdf_bytes)
            
            logger.info(f'PDF generated successfully for user: {request.user.email}, size: {len(pdf_bytes)} bytes')
            return response
            
        except PDFGenerationError as e:
            logger.error(f'PDF generation failed: {str(e)}')
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'Unexpected error during PDF generation: {str(e)}')
            return Response(
                {'error': 'An unexpected error occurred during PDF generation'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GeneratePreviewView(APIView):
    """
    Generate HTML preview from CV data
    Equivalent to Node.js generatePreview method
    """
    
    permission_classes = [IsAuthenticated]
    
    @method_decorator(ratelimit(key='user', rate='100/5min', method='POST'))
    def post(self, request):
        """Generate HTML preview from CV data"""
        try:
            user_email = getattr(request.user, 'email', 'anonymous')
            logger.info(f'Preview generation requested by user: {user_email}')
            
            # Validate request data
            serializer = PDFGenerationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            cv_data = serializer.validated_data['cv_data']
            visibility = serializer.validated_data['visibility']
            template_name = serializer.validated_data['template_name']
            
            # Generate HTML using template manager (lazy initialization)
            template_manager = TemplateManager()
            context = {
                'cv_data': cv_data,
                'visibility': visibility,
                'template_name': template_name,
                'generation_timestamp': timezone.now(),
            }
            html_content = template_manager.render_template(template_name, context)
            
            # Return HTML response
            response = HttpResponse(html_content, content_type='text/html')
            
            logger.info(f'Preview generated successfully for user: {user_email}')
            return response
            
        except PDFGenerationError as e:
            logger.error(f'Preview generation failed: {str(e)}')
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'Unexpected error during preview generation: {str(e)}')
            return Response(
                {'error': 'An unexpected error occurred during preview generation'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GeneratePDFFromHTMLView(APIView):
    """
    Generate PDF from pre-rendered HTML content
    Equivalent to Node.js generatePdfFromHtml method
    """
    
    permission_classes = [IsAuthenticated]
    
    @method_decorator(ratelimit(key='user', rate='10/15min', method='POST'))
    def post(self, request):
        """Generate PDF from HTML content"""
        try:
            logger.info(f'HTML-to-PDF generation requested by user: {request.user.email}')
            
            # Validate request data
            serializer = HTMLPDFGenerationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            html_content = serializer.validated_data['html_content']
            styles = serializer.validated_data['styles']
            cv_data = serializer.validated_data.get('cv_data')
            template_name = serializer.validated_data['template_name']
            
            logger.info(f'HTML content length: {len(html_content)}, styles length: {len(styles)}')
            
            # Generate PDF using WeasyPrint service
            weasyprint_service = WeasyPrintService()
            pdf_bytes = weasyprint_service.generate_pdf_from_html(html_content, styles)
            
            # Get suggested filename
            filename = 'CV_Resume.pdf'
            if cv_data:
                filename = weasyprint_service.get_suggested_filename(cv_data)
            
            # Create HTTP response with PDF
            response = HttpResponse(pdf_bytes, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Content-Length'] = len(pdf_bytes)
            
            logger.info(f'PDF generated from HTML successfully for user: {request.user.email}, size: {len(pdf_bytes)} bytes')
            return response
            
        except PDFGenerationError as e:
            logger.error(f'HTML-to-PDF generation failed: {str(e)}')
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f'Unexpected error during HTML-to-PDF generation: {str(e)}')
            return Response(
                {'error': 'An unexpected error occurred during HTML PDF generation'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TemplateListView(APIView):
    """
    Get available CV templates
    Equivalent to Node.js getTemplates method
    """
    
    permission_classes = []  # Public endpoint
    
    def get(self, request):
        """Get list of available templates"""
        try:
            template_manager = TemplateManager()
            templates = template_manager.get_template_list()
            
            logger.info(f'Templates list requested, returned {len(templates)} templates')
            return Response({'templates': templates})
            
        except Exception as e:
            logger.error(f'Failed to get templates: {str(e)}')
            return Response(
                {'error': 'Failed to get templates'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class GenerateAsyncPDFView(APIView):
    """
    Start async PDF generation task
    Returns task ID for status tracking
    """
    
    permission_classes = [IsAuthenticated]
    
    @method_decorator(ratelimit(key='user', rate='20/1h', method='POST'))
    def post(self, request):
        """Start async PDF generation"""
        try:
            logger.info(f'Async PDF generation requested by user: {request.user.email}')
            
            # Validate request data
            serializer = PDFGenerationSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            
            cv_data = serializer.validated_data['cv_data']
            visibility = serializer.validated_data['visibility']
            template_name = serializer.validated_data['template_name']
            
            # Get user ID from request (assuming Supabase JWT auth)
            user_id = str(request.user.id) if hasattr(request.user, 'id') else 'anonymous'
            
            # Create PDF generation log
            pdf_generation = PDFGenerationLog.objects.create(
                user_id=user_id,
                template_name=template_name,
                status='pending',
                priority='normal'
            )
            
            # Start async task
            task = generate_pdf_async.delay(
                generation_id=str(pdf_generation.id),
                cv_data=cv_data,
                visibility=visibility,
                template_name=template_name,
                user_id=user_id
            )
            
            # Update generation record with task ID
            pdf_generation.task_id = task.id
            pdf_generation.save()
            
            logger.info(f'Async PDF task started: {task.id} for generation: {pdf_generation.id}')
            
            return Response({
                'task_id': task.id,
                'generation_id': str(pdf_generation.id),
                'status': 'pending',
                'message': 'PDF generation started'
            }, status=status.HTTP_202_ACCEPTED)
            
        except Exception as e:
            logger.error(f'Failed to start async PDF generation: {str(e)}')
            return Response(
                {'error': 'Failed to start PDF generation'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PDFStatusView(APIView):
    """
    Check status of async PDF generation
    """
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request, generation_id):
        """Get PDF generation status"""
        try:
            # Get generation record
            generation = get_object_or_404(PDFGenerationLog, id=generation_id)
            
            # Check if user owns this generation
            user_id = str(request.user.id) if hasattr(request.user, 'id') else 'anonymous'
            if str(generation.user_id) != user_id:
                return Response(
                    {'error': 'Not authorized to view this generation'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            response_data = {
                'generation_id': str(generation.id),
                'status': generation.status,
                'created_at': generation.created_at.isoformat(),
                'template_name': generation.template_name,
            }
            
            # Add completion data if available
            if generation.status == 'completed':
                response_data.update({
                    'file_url': generation.file_url,
                    'file_size': generation.file_size,
                    'processing_duration': str(generation.processing_duration) if generation.processing_duration else None,
                    'expires_at': generation.expires_at.isoformat() if generation.expires_at else None,
                })
            elif generation.status == 'failed':
                response_data['error_message'] = generation.error_message
                response_data['retry_count'] = generation.retry_count
                response_data['can_retry'] = generation.can_retry
            elif generation.status in ['pending', 'processing']:
                if generation.task_id:
                    # Get task progress from Celery
                    from celery.result import AsyncResult
                    task_result = AsyncResult(generation.task_id)
                    if task_result.state == 'PROGRESS':
                        response_data['progress'] = task_result.info
            
            return Response(response_data)
            
        except Exception as e:
            logger.error(f'Failed to get PDF status: {str(e)}')
            return Response(
                {'error': 'Failed to get PDF status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class PDFHealthView(APIView):
    """
    Get PDF service health status
    Equivalent to Node.js getHealth method
    """
    
    permission_classes = []  # Public endpoint
    
    def get(self, request):
        """Get PDF service health status"""
        try:
            import sys
            import django
            from django.db import connection
            
            # Test database connection
            db_status = 'connected'
            try:
                with connection.cursor() as cursor:
                    cursor.execute("SELECT 1")
            except Exception:
                db_status = 'disconnected'
            
            # Test WeasyPrint service
            weasyprint_status = 'available'
            try:
                weasyprint_service = WeasyPrintService()
                service_health = weasyprint_service.check_service_health()
                weasyprint_status = service_health['status']
            except Exception as e:
                weasyprint_status = f'error: {str(e)}'
            
            health_data = {
                'status': 'healthy',
                'timestamp': None,  # Will be set by timezone if available
                'pdf_service': {
                    'engine': 'WeasyPrint',
                    'status': weasyprint_status,
                },
                'database': db_status,
                'version': '1.0.0',
                'django_version': django.VERSION,
                'python_version': sys.version,
            }
            
            # Set timestamp if timezone is available
            try:
                from django.utils import timezone
                health_data['timestamp'] = timezone.now().isoformat()
            except ImportError:
                pass
            
            # Determine overall status
            if db_status != 'connected' or weasyprint_status != 'available':
                health_data['status'] = 'degraded'
                status_code = status.HTTP_206_PARTIAL_CONTENT
            else:
                status_code = status.HTTP_200_OK
            
            logger.info('PDF service health check requested')
            return Response(health_data, status=status_code)
            
        except Exception as e:
            logger.error(f'Health check failed: {str(e)}')
            return Response({
                'status': 'unhealthy',
                'error': str(e)
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)