amqp==5.3.1
annotated-types==0.7.0
anyio==4.9.0
asgiref==3.9.1
Authlib==1.6.1
bandit==1.8.6
bidict==0.23.1
billiard==4.2.1
black==25.1.0
blinker==1.9.0
Brotli==1.1.0
cairocffi==1.7.1
CairoSVG==2.8.2
celery==5.5.3
certifi==2025.7.14
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==*******
click-repl==0.3.0
ConfigArgParse==1.7.1
coverage==7.10.1
cron-descriptor==1.4.5
cryptography==45.0.5
cssselect2==0.8.0
defusedxml==0.7.1
deprecation==2.1.0
Django==5.2.4
django-celery-beat==2.8.1
django-cors-headers==4.7.0
django-environ==0.11.2
django-extensions==3.2.3
django-prometheus==2.3.1
django-ratelimit==4.1.0
django-stubs==5.2.2
django-stubs-ext==5.2.2
django-timezone-field==7.1
djangorestframework==3.16.0
dparse==0.6.4
execnet==2.1.1
factory_boy==3.3.3
Faker==37.5.3
filelock==3.12.4
flake8==7.3.0
Flask==3.1.1
flask-cors==6.0.1
Flask-Login==0.6.3
flower==2.0.1
fonttools==4.59.0
freezegun==1.5.4
gevent==25.5.1
geventhttpclient==2.3.4
gotrue==2.12.3
greenlet==3.2.3
h11==0.16.0
h2==4.2.0
hpack==4.1.0
html5lib==1.1
httpcore==1.0.9
httpx==0.27.2
humanize==4.12.3
hyperframe==6.1.0
idna==3.10
iniconfig==2.1.0
isort==6.0.1
itsdangerous==2.2.0
Jinja2==3.1.4
joblib==1.5.1
kombu==5.5.4
locust==2.37.14
locust-cloud==1.26.3
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==4.0.0
mccabe==0.7.0
mdurl==0.1.2
memory-profiler==0.61.0
mirakuru==2.6.1
mock==5.2.0
msgpack==1.1.1
mypy==1.17.1
mypy_extensions==1.1.0
nltk==3.9.1
packaging==25.0
pathspec==0.12.1
pbr==6.1.1
pillow==11.3.0
platformdirs==4.3.8
pluggy==1.6.0
port-for==0.7.4
postgrest==1.1.1
pprintpp==0.4.0
prometheus_client==0.22.1
prompt_toolkit==3.0.51
psutil==6.0.0
psycopg==3.2.9
psycopg2-binary==2.9.10
py-cpuinfo==9.0.0
pycodestyle==2.14.0
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
pydyf==0.11.0
pyflakes==3.4.0
Pygments==2.19.2
PyJWT==2.10.1
pyphen==0.17.2
pytest==8.4.1
pytest-asyncio==1.1.0
pytest-benchmark==5.1.0
pytest-clarity==1.0.1
pytest-cov==6.2.1
pytest-django==4.11.1
pytest-html==4.1.1
pytest-metadata==3.1.1
pytest-mock==3.14.1
pytest-postgresql==7.0.2
pytest-sugar==1.0.0
pytest-timeout==2.4.0
pytest-xdist==3.8.0
python-crontab==3.3.0
python-dateutil==2.9.0
python-decouple==3.8
python-engineio==4.12.2
python-socketio==5.13.0
pytz==2025.2
PyYAML==6.0.2
pyzmq==27.0.0
realtime==2.6.0
redis==6.2.0
regex==2025.7.34
requests==2.32.3
responses==0.25.7
rich==14.1.0
ruamel.yaml==0.18.14
ruamel.yaml.clib==0.2.12
safety==3.2.9
safety-schemas==0.0.5
setuptools==80.9.0
shellingham==1.5.4
simple-websocket==1.1.0
six==1.17.0
sniffio==1.3.1
sqlparse==0.5.3
stevedore==5.4.1
storage3==0.12.0
StrEnum==0.4.15
supabase==2.17.0
supafunc==0.10.1
tenacity==9.1.2
termcolor==3.1.0
tinycss2==1.4.0
tinyhtml5==2.0.0
tomlkit==0.13.3
tornado==6.5.1
tqdm==4.67.1
typer==0.16.0
types-PyYAML==6.0.12.20250516
typing-inspection==0.4.1
typing_extensions==4.14.1
tzdata==2025.2
urllib3==2.5.0
vine==5.1.0
wcwidth==0.2.13
weasyprint==66.0
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wheel==0.45.1
wsproto==1.2.0
zope.event==5.1.1
zope.interface==7.2
zopfli==0.2.3.post1
