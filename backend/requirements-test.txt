# Testing Requirements for CVFlo Backend
# Run: pip install -r requirements-test.txt
# Note: No version constraints - let pip resolve dependencies automatically

# Core Testing Framework
pytest
pytest-django
pytest-cov
pytest-mock
pytest-xdist  # For parallel test execution
pytest-timeout
pytest-html  # HTML test reports

# Test Data Generation
factory-boy
faker

# Mocking and Patching
responses
freezegun  # Time mocking
mock

# Performance Testing
pytest-benchmark
locust  # Load testing

# Security Testing
bandit  # Security linting
safety  # Dependency vulnerability checking

# Coverage and Quality
coverage
pytest-clarity  # Better assertion output
pytest-sugar  # Better test output formatting

# Memory and Performance Monitoring
psutil
memory-profiler

# Database Testing
pytest-postgresql  # For PostgreSQL testing

# Async Testing
pytest-asyncio

# Code Quality
flake8
black
isort

# Type Checking
mypy
django-stubs

# Ensure compatible versions
pydantic