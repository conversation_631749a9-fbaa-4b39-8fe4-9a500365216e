"""
Celery configuration for CVFlo project.

This module configures Celery for async PDF generation with <PERSON><PERSON> as the broker,
including task routing, retry policies, and monitoring settings.
"""

import os
from celery import Celery

# Set the default Django settings module for the 'celery' program
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cvflo.settings')

# Create Celery instance
app = Celery('cvflo')

# Configure Celery using Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')

# Celery configuration
app.conf.update(
    # Broker settings
    broker_url=os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    result_backend=os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
    
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Task routing
    task_routes={
        'apps.pdf_generation.async_tasks.generate_pdf_async': {
            'queue': 'pdf_generation',
            'routing_key': 'pdf.generate',
        },
        'apps.pdf_generation.async_tasks.cleanup_expired_pdfs': {
            'queue': 'maintenance',
            'routing_key': 'maintenance.cleanup',
        },
        'apps.pdf_generation.async_tasks.health_check_pdf_service': {
            'queue': 'monitoring',
            'routing_key': 'monitoring.health',
        },
        'apps.pdf_generation.async_tasks.batch_generate_pdfs': {
            'queue': 'batch_processing',
            'routing_key': 'batch.pdf',
        },
    },
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=100,
    worker_disable_rate_limits=False,
    
    # Task execution settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_track_started=True,
    task_time_limit=300,  # 5 minutes
    task_soft_time_limit=240,  # 4 minutes
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        'master_name': 'mymaster',
        'retry_on_timeout': True,
        'socket_keepalive': True,
        'socket_keepalive_options': {
            'TCP_KEEPIDLE': 1,
            'TCP_KEEPINTVL': 3,
            'TCP_KEEPCNT': 5,
        },
    },
    
    # Monitoring and logging
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'cleanup-expired-pdfs': {
            'task': 'apps.pdf_generation.async_tasks.cleanup_expired_pdfs',
            'schedule': 3600.0,  # Every hour
            'options': {'queue': 'maintenance'}
        },
        'health-check-pdf-service': {
            'task': 'apps.pdf_generation.async_tasks.health_check_pdf_service',
            'schedule': 300.0,  # Every 5 minutes
            'options': {'queue': 'monitoring'}
        },
    },
    
    # Security settings
    worker_hijack_root_logger=False,
    worker_log_color=False,
    
    # Memory management
    worker_max_memory_per_child=512000,  # 512MB
)

# Auto-discover tasks from all registered Django apps
app.autodiscover_tasks()

# Task debugging
@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')


# Celery signals for monitoring
from celery.signals import task_prerun, task_postrun, task_failure

@task_prerun.connect
def task_prerun_handler(task_id, task, *args, **kwargs):
    """Handle task pre-run"""
    import logging
    logger = logging.getLogger('cvflo.celery')
    logger.info(f'Task {task.name}[{task_id}] started')

@task_postrun.connect
def task_postrun_handler(task_id, task, *args, **kwargs):
    """Handle task post-run"""
    import logging
    logger = logging.getLogger('cvflo.celery')
    logger.info(f'Task {task.name}[{task_id}] completed')

@task_failure.connect
def task_failure_handler(task_id, exception, traceback, einfo, *args, **kwargs):
    """Handle task failure"""
    import logging
    logger = logging.getLogger('cvflo.celery')
    logger.error(f'Task {task_id} failed: {exception}')


# Custom task base class for better error handling
class BaseTask(app.Task):
    """Base task class with enhanced error handling"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Called on task failure"""
        import logging
        logger = logging.getLogger('cvflo.celery')
        logger.error(f'Task {self.name}[{task_id}] failed: {exc}')
        
        # Update task status in database if applicable
        if 'generation_id' in kwargs:
            try:
                from apps.cv_builder.models import PDFGenerationLog
                generation = PDFGenerationLog.objects.get(id=kwargs['generation_id'])
                generation.status = 'failed'
                generation.error_message = str(exc)
                generation.save()
            except Exception as e:
                logger.error(f'Failed to update generation status: {e}')
    
    def on_success(self, retval, task_id, args, kwargs):
        """Called on task success"""
        import logging
        logger = logging.getLogger('cvflo.celery')
        logger.info(f'Task {self.name}[{task_id}] succeeded')
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Called on task retry"""
        import logging
        logger = logging.getLogger('cvflo.celery')
        logger.warning(f'Task {self.name}[{task_id}] retrying: {exc}')

# Set default task base class
app.Task = BaseTask
