# Security Architecture Refactor Plan

## Current Problems
- 4 separate security files with overlapping responsibilities
- Code duplication across modules
- Inconsistent security implementations
- Hard to maintain and test

## Proposed Architecture

### 1. Single Security Module Structure
```
backend/apps/core/security/
├── __init__.py              # Main exports
├── authentication.py       # JWT, Supabase auth
├── authorization.py         # Permissions, RBAC
├── middleware.py           # All security middleware
├── rate_limiting.py        # Rate limiting logic
├── validation.py           # Input validation
├── audit.py                # Security logging
└── exceptions.py           # Security exceptions
```

### 2. Clear Separation of Concerns

#### `authentication.py`
- Single JWT validation implementation
- Supabase client management
- Token verification (local + API fallback)
- User authentication logic

#### `authorization.py`
- Permission checking
- Role-based access control
- Resource-level permissions

#### `middleware.py`
- Single security middleware
- Request/response security headers
- Authentication enforcement
- Rate limiting enforcement

#### `rate_limiting.py`
- Centralized rate limiting
- Different strategies (user, IP, endpoint)
- Redis/cache integration

#### `validation.py`
- Input sanitization
- CV data validation
- File upload validation

#### `audit.py`
- Security event logging
- Failed login tracking
- Suspicious activity detection

### 3. Usage Pattern
```python
# In views
from apps.core.security import require_auth, validate_cv_data, rate_limit

@require_auth
@rate_limit('pdf_generation', '10/hour')
def generate_pdf(request):
    cv_data = validate_cv_data(request.data)
    # ... business logic
```

### 4. Configuration
```python
# settings.py
SECURITY = {
    'JWT_VALIDATION': {
        'LOCAL_VERIFICATION': not DEBUG,
        'FALLBACK_TO_API': True,
    },
    'RATE_LIMITING': {
        'PDF_GENERATION': '10/hour',
        'API_CALLS': '100/hour',
    },
    'AUDIT_LOGGING': {
        'ENABLED': True,
        'LOG_LEVEL': 'INFO',
    }
}
```

## Migration Strategy

### Phase 1: Create New Structure
1. Create `apps/core/security/` package
2. Move common functionality to new modules
3. Keep old files for backward compatibility

### Phase 2: Update Imports
1. Update all imports to use new security package
2. Remove duplicated code
3. Standardize security implementations

### Phase 3: Clean Up
1. Remove old security files
2. Update tests
3. Update documentation

## Benefits
- ✅ Single source of truth for security
- ✅ Easier to maintain and update
- ✅ Consistent security across the app
- ✅ Better testability
- ✅ Clear separation of concerns
- ✅ Easier to add new security features
