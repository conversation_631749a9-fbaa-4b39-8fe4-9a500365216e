"""
Comprehensive Authentication Tests for Supabase Integration

Tests all authentication components including:
- JWT token verification and validation
- User creation and synchronization
- Authentication backends and middleware
- Caching and performance optimization
- Error handling and edge cases
"""

import uuid
import json
import jwt
from datetime import datetime, timedelta, timezone as tz
from unittest.mock import patch, Mock, MagicMock

from django.test import TestCase, RequestFactory, override_settings
from django.contrib.auth.models import User
from django.core.cache import cache
from django.utils import timezone
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.test import APIRequestFactory

from apps.authentication.backends import (
    SupabaseService, SupabaseAuthentication, SupabaseBackend,
    OptionalSupabaseAuthentication
)
from apps.cv_builder.models import UserProfile
from .factories import SupabaseUserDataFactory, UserFactory
from .mocks import (
    MockSupabaseClient, mock_supabase_service, setup_mock_supabase_user,
    apply_test_settings
)


@apply_test_settings()
class SupabaseServiceTest(TestCase):
    """Test SupabaseService functionality"""
    
    def setUp(self):
        self.factory = RequestFactory()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.authentication.backends.create_client')
    def test_supabase_service_initialization(self, mock_create_client):
        """Test SupabaseService initialization"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        service = SupabaseService()
        
        self.assertEqual(service.url, 'https://mock.supabase.co')
        self.assertEqual(service.anon_key, 'mock-anon-key')
        self.assertEqual(service.service_role_key, 'mock-service-key')
        self.assertEqual(service.client, mock_client)
        self.assertEqual(service.admin_client, mock_client)
    
    @patch('apps.authentication.backends.create_client')
    def test_supabase_service_missing_config(self, mock_create_client):
        """Test SupabaseService with missing configuration"""
        with override_settings(SUPABASE_URL=None):
            with self.assertRaises(ValueError) as context:
                SupabaseService()
            
            self.assertIn('Supabase URL and ANON_KEY must be configured', str(context.exception))
    
    def test_token_format_validation(self):
        """Test JWT token format validation"""
        with mock_supabase_service() as (service, mock_client):
            # Valid JWT format
            valid_token = 'header.payload.signature'
            self.assertTrue(service.validate_token_format(valid_token))
            
            # Invalid formats
            invalid_tokens = [
                None,
                '',
                'invalid',
                'header.payload',
                'header..signature',
                123,
                [],
            ]
            
            for invalid_token in invalid_tokens:
                self.assertFalse(service.validate_token_format(invalid_token))
    
    def test_verify_token_success(self):
        """Test successful token verification"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'valid-jwt-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            result = service.verify_token(token)
            
            self.assertEqual(result['id'], user_data['id'])
            self.assertEqual(result['email'], user_data['email'])
            self.assertTrue(result['email_verified'])
    
    def test_verify_token_with_jwt_secret(self):
        """Test token verification with local JWT secret"""
        user_data = SupabaseUserDataFactory.create_user_data()
        jwt_payload = SupabaseUserDataFactory.create_jwt_token_payload(user_data)
        
        with override_settings(SUPABASE_JWT_SECRET='test-secret'):
            token = jwt.encode(jwt_payload, 'test-secret', algorithm='HS256')
            
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, token, user_data)
                
                result = service.verify_token(token)
                
                self.assertEqual(result['id'], user_data['id'])
                self.assertEqual(result['email'], user_data['email'])
    
    def test_verify_token_expired_jwt(self):
        """Test verification of expired JWT token"""
        user_data = SupabaseUserDataFactory.create_user_data()
        jwt_payload = SupabaseUserDataFactory.create_jwt_token_payload(user_data)
        jwt_payload['exp'] = int((timezone.now() - timedelta(hours=1)).timestamp())
        
        with override_settings(SUPABASE_JWT_SECRET='test-secret'):
            expired_token = jwt.encode(jwt_payload, 'test-secret', algorithm='HS256')
            
            with mock_supabase_service() as (service, mock_client):
                with self.assertRaises(AuthenticationFailed) as context:
                    service.verify_token(expired_token)
                
                self.assertIn('Invalid or expired token', str(context.exception))
    
    def test_verify_token_invalid_token(self):
        """Test verification of invalid token"""
        invalid_token = 'invalid-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, invalid_token, None)
            
            with self.assertRaises(AuthenticationFailed) as context:
                service.verify_token(invalid_token)
            
            self.assertIn('Invalid token', str(context.exception))
    
    def test_verify_token_caching(self):
        """Test token verification caching"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'cached-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            # First call should fetch from Supabase
            result1 = service.verify_token(token)
            
            # Second call should use cache
            with patch.object(mock_client.auth, 'get_user') as mock_get_user:
                result2 = service.verify_token(token)
                mock_get_user.assert_not_called()  # Should not call API
            
            self.assertEqual(result1, result2)
    
    def test_get_or_create_django_user_new_user(self):
        """Test creating new Django user from Supabase data"""
        supabase_user = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>',
            first_name='John',
            last_name='Doe'
        )
        
        with mock_supabase_service() as (service, mock_client):
            django_user = service.get_or_create_django_user(supabase_user)
            
            self.assertEqual(django_user.username, supabase_user['id'])
            self.assertEqual(django_user.email, supabase_user['email'])
            self.assertEqual(django_user.first_name, 'John')
            self.assertEqual(django_user.last_name, 'Doe')
            self.assertTrue(django_user.is_active)
    
    def test_get_or_create_django_user_existing_by_username(self):
        """Test finding existing Django user by username (Supabase ID)"""
        supabase_user = SupabaseUserDataFactory.create_user_data()
        existing_user = UserFactory(
            username=supabase_user['id'],
            email=supabase_user['email']
        )
        
        with mock_supabase_service() as (service, mock_client):
            django_user = service.get_or_create_django_user(supabase_user)
            
            self.assertEqual(django_user.id, existing_user.id)
            self.assertEqual(django_user.username, supabase_user['id'])
    
    def test_get_or_create_django_user_existing_by_email(self):
        """Test finding existing Django user by email and updating username"""
        supabase_user = SupabaseUserDataFactory.create_user_data()
        existing_user = UserFactory(
            username='old-username',
            email=supabase_user['email']
        )
        
        with mock_supabase_service() as (service, mock_client):
            django_user = service.get_or_create_django_user(supabase_user)
            
            self.assertEqual(django_user.id, existing_user.id)
            self.assertEqual(django_user.username, supabase_user['id'])  # Updated
            self.assertEqual(django_user.email, supabase_user['email'])
    
    def test_get_or_create_django_user_name_parsing(self):
        """Test various name parsing scenarios"""
        # Test full_name parsing
        supabase_user = SupabaseUserDataFactory.create_user_data()
        supabase_user['user_metadata'] = {
            'full_name': 'Jane Elizabeth Smith'
        }
        
        with mock_supabase_service() as (service, mock_client):
            django_user = service.get_or_create_django_user(supabase_user)
            
            self.assertEqual(django_user.first_name, 'Jane')
            self.assertEqual(django_user.last_name, 'Elizabeth Smith')
    
    def test_get_or_create_django_user_email_fallback(self):
        """Test name fallback to email username"""
        supabase_user = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>'
        )
        supabase_user['user_metadata'] = {}  # No name info
        
        with mock_supabase_service() as (service, mock_client):
            django_user = service.get_or_create_django_user(supabase_user)
            
            self.assertEqual(django_user.first_name, 'John Doe')
    
    def test_get_or_create_django_user_inactive_unverified(self):
        """Test creating inactive user for unverified email"""
        supabase_user = SupabaseUserDataFactory.create_user_data(
            email_verified=False
        )
        
        with mock_supabase_service() as (service, mock_client):
            django_user = service.get_or_create_django_user(supabase_user)
            
            self.assertFalse(django_user.is_active)
    
    def test_get_or_create_django_user_caching(self):
        """Test user creation caching"""
        supabase_user = SupabaseUserDataFactory.create_user_data()
        
        with mock_supabase_service() as (service, mock_client):
            # First call creates user
            django_user1 = service.get_or_create_django_user(supabase_user)
            
            # Second call should use cache
            with patch('apps.cv_builder.models.UserProfile.objects.get_or_create') as mock_profile:
                django_user2 = service.get_or_create_django_user(supabase_user)
                mock_profile.assert_not_called()  # Should use cached user
            
            self.assertEqual(django_user1.id, django_user2.id)
    
    def test_sync_user_profile(self):
        """Test user profile synchronization"""
        django_user = UserFactory()
        supabase_user = SupabaseUserDataFactory.create_user_data()
        
        with mock_supabase_service() as (service, mock_client):
            service.sync_user_profile(django_user, supabase_user)
            
            # Check UserProfile was created
            profile = UserProfile.objects.get(id=supabase_user['id'])
            self.assertEqual(profile.email, supabase_user['email'])
    
    def test_update_user_info(self):
        """Test updating Django user info from Supabase"""
        django_user = UserFactory(
            email='<EMAIL>',
            is_active=False
        )
        supabase_user = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>',
            email_verified=True
        )
        
        with mock_supabase_service() as (service, mock_client):
            service.update_user_info(django_user, supabase_user)
            
            django_user.refresh_from_db()
            self.assertEqual(django_user.email, '<EMAIL>')
            self.assertTrue(django_user.is_active)


@apply_test_settings()
class SupabaseAuthenticationTest(TestCase):
    """Test SupabaseAuthentication DRF class"""
    
    def setUp(self):
        self.factory = APIRequestFactory()
        self.auth = SupabaseAuthentication()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_extract_token_valid_bearer(self):
        """Test extracting valid Bearer token"""
        token = 'valid-jwt-token'
        auth_header = f'Bearer {token}'
        
        extracted_token = self.auth.extract_token(auth_header)
        self.assertEqual(extracted_token, token)
    
    def test_extract_token_invalid_format(self):
        """Test extracting token with invalid format"""
        invalid_headers = [
            'InvalidBearer token',
            'Bearer',
            'Bearer token extra',
            'Basic dXNlcjpwYXNz',
            '',
            None
        ]
        
        for header in invalid_headers:
            extracted_token = self.auth.extract_token(header)
            self.assertIsNone(extracted_token)
    
    def test_authenticate_no_header(self):
        """Test authentication without Authorization header"""
        request = self.factory.get('/')
        
        result = self.auth.authenticate(request)
        self.assertIsNone(result)
    
    def test_authenticate_invalid_token_format(self):
        """Test authentication with invalid token format"""
        request = self.factory.get('/', HTTP_AUTHORIZATION='Bearer invalid')
        
        with self.assertRaises(AuthenticationFailed) as context:
            self.auth.authenticate(request)
        
        self.assertIn('Invalid token format', str(context.exception))
    
    def test_authenticate_success(self):
        """Test successful authentication"""
        user_data = SupabaseUserDataFactory.create_user_data(email_verified=True)
        token = 'valid.jwt.token'
        request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            with patch.object(self.auth, 'supabase_service', service):
                user, auth_token = self.auth.authenticate(request)
                
                self.assertIsInstance(user, User)
                self.assertEqual(user.email, user_data['email'])
                self.assertEqual(auth_token, token)
                self.assertEqual(request.user_id, user_data['id'])
                self.assertEqual(request.supabase_user, user_data)
    
    def test_authenticate_inactive_user(self):
        """Test authentication with inactive user"""
        user_data = SupabaseUserDataFactory.create_user_data(email_verified=False)
        token = 'valid.jwt.token'
        request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            with patch.object(self.auth, 'supabase_service', service):
                with self.assertRaises(AuthenticationFailed) as context:
                    self.auth.authenticate(request)
                
                self.assertIn('User account is inactive', str(context.exception))
    
    def test_authenticate_invalid_token(self):
        """Test authentication with invalid token"""
        token = 'invalid.jwt.token'
        request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, None)
            
            with patch.object(self.auth, 'supabase_service', service):
                with self.assertRaises(AuthenticationFailed):
                    self.auth.authenticate(request)
    
    def test_authenticate_header(self):
        """Test authentication header for 401 responses"""
        request = self.factory.get('/')
        header = self.auth.authenticate_header(request)
        self.assertEqual(header, 'Bearer')


@apply_test_settings()
class SupabaseBackendTest(TestCase):
    """Test SupabaseBackend for session authentication"""
    
    def setUp(self):
        self.factory = RequestFactory()
        self.backend = SupabaseBackend()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_authenticate_success(self):
        """Test successful backend authentication"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'valid-jwt-token'
        request = self.factory.get('/')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            with patch.object(self.backend, 'supabase_service', service):
                user = self.backend.authenticate(request, token=token)
                
                self.assertIsInstance(user, User)
                self.assertEqual(user.email, user_data['email'])
    
    def test_authenticate_no_token(self):
        """Test backend authentication without token"""
        request = self.factory.get('/')
        
        user = self.backend.authenticate(request)
        self.assertIsNone(user)
    
    def test_authenticate_invalid_token(self):
        """Test backend authentication with invalid token"""
        token = 'invalid-token'
        request = self.factory.get('/')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, None)
            
            with patch.object(self.backend, 'supabase_service', service):
                user = self.backend.authenticate(request, token=token)
                self.assertIsNone(user)
    
    def test_get_user_exists(self):
        """Test getting existing user by ID"""
        existing_user = UserFactory()
        
        user = self.backend.get_user(existing_user.id)
        self.assertEqual(user, existing_user)
    
    def test_get_user_not_exists(self):
        """Test getting non-existent user"""
        user = self.backend.get_user(99999)
        self.assertIsNone(user)


@apply_test_settings()
class OptionalSupabaseAuthenticationTest(TestCase):
    """Test OptionalSupabaseAuthentication for optional auth"""
    
    def setUp(self):
        self.factory = APIRequestFactory()
        self.auth = OptionalSupabaseAuthentication()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_authenticate_success(self):
        """Test successful optional authentication"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'valid.jwt.token'
        request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            with patch.object(self.auth, 'supabase_service', service):
                result = self.auth.authenticate(request)
                
                self.assertIsNotNone(result)
                user, auth_token = result
                self.assertEqual(user.email, user_data['email'])
    
    def test_authenticate_failed_returns_none(self):
        """Test that failed optional authentication returns None instead of raising"""
        request = self.factory.get('/', HTTP_AUTHORIZATION='Bearer invalid')
        
        result = self.auth.authenticate(request)
        self.assertIsNone(result)
    
    def test_authenticate_no_token_returns_none(self):
        """Test that optional authentication without token returns None"""
        request = self.factory.get('/')
        
        result = self.auth.authenticate(request)
        self.assertIsNone(result)


class AuthenticationIntegrationTest(TestCase):
    """Integration tests for authentication flow"""
    
    def setUp(self):
        self.factory = APIRequestFactory()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_full_authentication_flow(self):
        """Test complete authentication flow from token to user"""
        # Create Supabase user data
        user_data = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>',
            first_name='Integration',
            last_name='Test'
        )
        token = 'integration.test.token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            # Test authentication
            auth = SupabaseAuthentication()
            request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
            
            with patch.object(auth, 'supabase_service', service):
                user, auth_token = auth.authenticate(request)
                
                # Verify Django user was created correctly
                self.assertEqual(user.username, user_data['id'])
                self.assertEqual(user.email, '<EMAIL>')
                self.assertEqual(user.first_name, 'Integration')
                self.assertEqual(user.last_name, 'Test')
                self.assertTrue(user.is_active)
                
                # Verify UserProfile was created
                profile = UserProfile.objects.get(id=user_data['id'])
                self.assertEqual(profile.email, '<EMAIL>')
                
                # Verify request attributes
                self.assertEqual(request.user_id, user_data['id'])
                self.assertEqual(request.supabase_user, user_data)
    
    def test_user_update_flow(self):
        """Test user update when Supabase data changes"""
        # Create initial user
        user_data = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>'
        )
        token = 'update.test.token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            auth = SupabaseAuthentication()
            request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
            
            with patch.object(auth, 'supabase_service', service):
                user1, _ = auth.authenticate(request)
                
                # Update Supabase user data
                updated_user_data = user_data.copy()
                updated_user_data['email'] = '<EMAIL>'
                setup_mock_supabase_user(mock_client, token, updated_user_data)
                
                # Clear cache to force update
                cache.clear()
                
                # Authenticate again
                request2 = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
                user2, _ = auth.authenticate(request2)
                
                # Should be same user instance but with updated email
                self.assertEqual(user1.id, user2.id)
                user2.refresh_from_db()
                self.assertEqual(user2.email, '<EMAIL>')


class AuthenticationErrorHandlingTest(TestCase):
    """Test error handling in authentication components"""
    
    def setUp(self):
        self.factory = APIRequestFactory()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_supabase_api_error(self):
        """Test handling of Supabase API errors"""
        token = 'api.error.token'
        request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        
        with mock_supabase_service() as (service, mock_client):
            # Mock API error
            mock_client.auth.get_user.side_effect = Exception('Supabase API Error')
            
            auth = SupabaseAuthentication()
            with patch.object(auth, 'supabase_service', service):
                with self.assertRaises(AuthenticationFailed) as context:
                    auth.authenticate(request)
                
                self.assertIn('Invalid or expired token', str(context.exception))
    
    def test_database_error_during_user_creation(self):
        """Test handling of database errors during user creation"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'db.error.token'
        request = self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            auth = SupabaseAuthentication()
            with patch.object(auth, 'supabase_service', service):
                with patch('django.contrib.auth.models.User.objects.create_user') as mock_create:
                    mock_create.side_effect = Exception('Database Error')
                    
                    with self.assertRaises(AuthenticationFailed) as context:
                        auth.authenticate(request)
                    
                    self.assertIn('Failed to authenticate user', str(context.exception))
    
    def test_cache_error_handling(self):
        """Test handling of cache errors"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'cache.error.token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            with patch('django.core.cache.cache.get') as mock_cache_get:
                mock_cache_get.side_effect = Exception('Cache Error')
                
                # Should still work without cache
                result = service.verify_token(token)
                self.assertEqual(result['id'], user_data['id'])


class AuthenticationPerformanceTest(TestCase):
    """Test authentication performance and caching"""
    
    def setUp(self):
        self.factory = APIRequestFactory()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_token_verification_caching(self):
        """Test that token verification results are properly cached"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'performance.test.token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            # First verification should call Supabase
            result1 = service.verify_token(token)
            call_count_after_first = mock_client.auth.get_user.call_count
            
            # Second verification should use cache
            result2 = service.verify_token(token)
            call_count_after_second = mock_client.auth.get_user.call_count
            
            self.assertEqual(result1, result2)
            self.assertEqual(call_count_after_first, call_count_after_second)
    
    def test_user_creation_caching(self):
        """Test that user creation results are properly cached"""
        user_data = SupabaseUserDataFactory.create_user_data()
        
        with mock_supabase_service() as (service, mock_client):
            # First call creates user
            user1 = service.get_or_create_django_user(user_data)
            
            # Second call should use cache
            with patch('django.contrib.auth.models.User.objects.get') as mock_get:
                mock_get.return_value = user1
                user2 = service.get_or_create_django_user(user_data)
                mock_get.assert_called_once()
            
            self.assertEqual(user1.id, user2.id)
    
    def test_multiple_concurrent_authentications(self):
        """Test multiple concurrent authentication attempts"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'concurrent.test.token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            auth = SupabaseAuthentication()
            
            # Simulate multiple concurrent requests
            requests = [
                self.factory.get('/', HTTP_AUTHORIZATION=f'Bearer {token}')
                for _ in range(5)
            ]
            
            with patch.object(auth, 'supabase_service', service):
                users = []
                for request in requests:
                    user, _ = auth.authenticate(request)
                    users.append(user)
                
                # All should return the same user
                first_user = users[0]
                for user in users[1:]:
                    self.assertEqual(user.id, first_user.id)
