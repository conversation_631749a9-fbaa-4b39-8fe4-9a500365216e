"""
Comprehensive WeasyPrint Service Tests

Tests all WeasyPrint service functionality including:
- PDF generation from CV data
- Template rendering and CSS handling
- Memory management and resource pooling
- Error handling and timeout protection
- Performance optimization and caching
- PDF service integration and API endpoints
- Template service functionality
"""

import io
import os
import gc
import tempfile
from unittest.mock import patch, Mo<PERSON>, MagicMock, call
from concurrent.futures import TimeoutError as FutureTimeoutError
from datetime import datetime, timedelta

from django.test import TestCase, override_settings
from django.utils import timezone
from django.core.cache import cache

from apps.pdf_generation.weasyprint_service import (
    WeasyPrintService, WeasyPrintPools, FontManager, MemoryManager
)
from apps.pdf_generation.exceptions import (
    PDFGenerationError, TemplateNotFoundError, PDFServiceUnavailableError,
    PDFTimeoutError, PDFMemoryError
)
# Legacy services have been consolidated into WeasyPrintService
# CVPDFService and TemplateService are deprecated
CVPDFService = None
TemplateService = None
from .factories import CVDataFactory
from .mocks import (
    MockWeasyPrintDocument, MockWeasyPrintCSS, MockWeasyPrintFontConfiguration,
    mock_weasyprint, apply_test_settings, create_mock_pdf_content
)


@apply_test_settings()
class WeasyPrintServiceInitializationTest(TestCase):
    """Test WeasyPrint service initialization and configuration"""
    
    def setUp(self):
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_service_initialization_success(self):
        """Test successful service initialization"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            self.assertIsNotNone(service.pools)
            self.assertIsNotNone(service.font_manager)
            self.assertIsNotNone(service.memory_manager)
            self.assertIsNotNone(service.template_manager)
            self.assertEqual(service.base_url, '/static/')
            self.assertEqual(service.cache_timeout, 300)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', False)
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_IMPORT_ERROR', 'WeasyPrint not installed')
    def test_service_initialization_weasyprint_unavailable(self):
        """Test service initialization when WeasyPrint is unavailable"""
        with self.assertRaises(PDFServiceUnavailableError) as context:
            WeasyPrintService()
        
        self.assertIn('WeasyPrint is not available', str(context.exception))
        self.assertIn('WeasyPrint not installed', str(context.exception))
    
    @override_settings(
        WEASYPRINT_MAX_WORKERS=5,
        WEASYPRINT_TIMEOUT=120,
        WEASYPRINT_MAX_MEMORY_MB=1024,
        WEASYPRINT_CACHE_TIMEOUT=600
    )
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_service_initialization_custom_settings(self):
        """Test service initialization with custom settings"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            self.assertEqual(service.pools.max_workers, 5)
            self.assertEqual(service.pools.timeout, 120)
            self.assertEqual(service.memory_manager.max_memory_mb, 1024)
            self.assertEqual(service.cache_timeout, 600)


class WeasyPrintPoolsTest(TestCase):
    """Test WeasyPrint thread pool management"""
    
    def test_pools_initialization(self):
        """Test pools initialization"""
        pools = WeasyPrintPools(max_workers=2, timeout=30)
        
        self.assertEqual(pools.max_workers, 2)
        self.assertEqual(pools.timeout, 30)
        self.assertIsNone(pools._executor)
    
    def test_get_executor_creates_pool(self):
        """Test that get_executor creates thread pool"""
        pools = WeasyPrintPools(max_workers=2)
        
        executor = pools.get_executor()
        
        self.assertIsNotNone(executor)
        self.assertEqual(executor._max_workers, 2)
        self.assertEqual(pools._executor, executor)
    
    def test_get_executor_reuses_pool(self):
        """Test that get_executor reuses existing pool"""
        pools = WeasyPrintPools()
        
        executor1 = pools.get_executor()
        executor2 = pools.get_executor()
        
        self.assertEqual(executor1, executor2)
    
    def test_pools_shutdown(self):
        """Test pools shutdown"""
        pools = WeasyPrintPools()
        executor = pools.get_executor()
        
        pools.shutdown()
        
        self.assertTrue(executor._shutdown)


class FontManagerTest(TestCase):
    """Test font configuration and management"""
    
    def test_font_manager_initialization(self):
        """Test font manager initialization"""
        font_manager = FontManager()
        
        self.assertIsNone(font_manager._font_config)
        self.assertEqual(font_manager._font_cache, {})
    
    @patch('apps.pdf_generation.weasyprint_service.FontConfiguration')
    def test_get_font_config_creates_configuration(self, mock_font_config_class):
        """Test that get_font_config creates FontConfiguration"""
        mock_font_config = Mock()
        mock_font_config_class.return_value = mock_font_config
        
        font_manager = FontManager()
        config = font_manager.get_font_config()
        
        self.assertEqual(config, mock_font_config)
        self.assertEqual(font_manager._font_config, mock_font_config)
        mock_font_config_class.assert_called_once()
    
    @patch('apps.pdf_generation.weasyprint_service.FontConfiguration')
    def test_get_font_config_reuses_configuration(self, mock_font_config_class):
        """Test that get_font_config reuses existing configuration"""
        mock_font_config = Mock()
        mock_font_config_class.return_value = mock_font_config
        
        font_manager = FontManager()
        config1 = font_manager.get_font_config()
        config2 = font_manager.get_font_config()
        
        self.assertEqual(config1, config2)
        mock_font_config_class.assert_called_once()  # Should only be called once


class MemoryManagerTest(TestCase):
    """Test memory management functionality"""
    
    def test_memory_manager_initialization(self):
        """Test memory manager initialization"""
        memory_manager = MemoryManager(max_memory_mb=256)
        
        self.assertEqual(memory_manager.max_memory_mb, 256)
    
    @patch('psutil.Process')
    def test_check_memory_usage_normal(self, mock_process_class):
        """Test normal memory usage check"""
        mock_process = Mock()
        mock_process.memory_info.return_value.rss = 100 * 1024 * 1024  # 100MB
        mock_process_class.return_value = mock_process
        
        memory_manager = MemoryManager(max_memory_mb=256)
        memory_mb = memory_manager.check_memory_usage()
        
        self.assertEqual(memory_mb, 100.0)
    
    @patch('psutil.Process')
    @patch('gc.collect')
    def test_check_memory_usage_high(self, mock_gc_collect, mock_process_class):
        """Test high memory usage triggers cleanup"""
        mock_process = Mock()
        mock_process.memory_info.return_value.rss = 600 * 1024 * 1024  # 600MB
        mock_process_class.return_value = mock_process
        
        memory_manager = MemoryManager(max_memory_mb=256)
        memory_mb = memory_manager.check_memory_usage()
        
        self.assertEqual(memory_mb, 600.0)
        mock_gc_collect.assert_called_once()
    
    def test_check_memory_usage_no_psutil(self):
        """Test memory check when psutil is not available"""
        with patch('apps.pdf_generation.weasyprint_service.psutil', None):
            memory_manager = MemoryManager()
            memory_mb = memory_manager.check_memory_usage()
            
            self.assertEqual(memory_mb, 0)


@apply_test_settings()
class WeasyPrintServicePDFGenerationTest(TestCase):
    """Test PDF generation functionality"""
    
    def setUp(self):
        cache.clear()
        self.cv_data = CVDataFactory.complete_cv_data()
        self.visibility = CVDataFactory.visibility_settings()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_success(self):
        """Test successful PDF generation"""
        with mock_weasyprint() as mocks:
            service = WeasyPrintService()
            
            pdf_bytes = service.generate_pdf(
                cv_data=self.cv_data,
                visibility=self.visibility,
                template_name='modern-0'
            )
            
            self.assertIsInstance(pdf_bytes, bytes)
            self.assertGreater(len(pdf_bytes), 0)
            self.assertTrue(pdf_bytes.startswith(b'%PDF-1.4'))
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_invalid_cv_data(self):
        """Test PDF generation with invalid CV data"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with self.assertRaises(PDFGenerationError) as context:
                service.generate_pdf(
                    cv_data="invalid",  # Should be dict
                    visibility=self.visibility,
                    template_name='modern-0'
                )
            
            self.assertIn('CV data must be a dictionary', str(context.exception))
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_invalid_template(self):
        """Test PDF generation with invalid template"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service.template_manager, 'template_exists', return_value=False):
                with self.assertRaises(TemplateNotFoundError) as context:
                    service.generate_pdf(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name='nonexistent-template'
                    )
                
                self.assertIn('Template \'nonexistent-template\' not found', str(context.exception))
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_with_options(self):
        """Test PDF generation with custom options"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            options = {
                'pdf_version': '1.7',
                'pdf_identifier': 'custom-id'
            }
            
            pdf_bytes = service.generate_pdf(
                cv_data=self.cv_data,
                visibility=self.visibility,
                template_name='classic-0',
                options=options
            )
            
            self.assertIsInstance(pdf_bytes, bytes)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_timeout(self):
        """Test PDF generation timeout"""
        with mock_weasyprint():
            service = WeasyPrintService()
            service.pools.timeout = 0.1  # Very short timeout
            
            with patch.object(service, '_generate_pdf_core') as mock_core:
                mock_core.side_effect = lambda *args: __import__('time').sleep(1)  # Takes too long
                
                with self.assertRaises(PDFTimeoutError) as context:
                    service.generate_pdf(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name='modern-0'
                    )
                
                self.assertIn('PDF generation timed out', str(context.exception))
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_from_html(self):
        """Test PDF generation directly from HTML"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            html_content = "<html><body><h1>Test CV</h1></body></html>"
            css_content = "body { font-family: Arial; }"
            
            pdf_bytes = service.generate_pdf_from_html(
                html_content=html_content,
                css_content=css_content
            )
            
            self.assertIsInstance(pdf_bytes, bytes)
            self.assertGreater(len(pdf_bytes), 0)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_empty_result(self):
        """Test handling of empty PDF result"""
        with mock_weasyprint() as mocks:
            # Mock empty PDF generation
            mock_doc = Mock()
            mock_doc.write_pdf.return_value = None
            mocks['HTML'].return_value = mock_doc
            
            with patch('io.BytesIO') as mock_bytes_io:
                mock_buffer = Mock()
                mock_buffer.getvalue.return_value = b''  # Empty bytes
                mock_bytes_io.return_value = mock_buffer
                
                service = WeasyPrintService()
                
                with self.assertRaises(PDFGenerationError) as context:
                    service.generate_pdf(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name='modern-0'
                    )
                
                self.assertIn('Generated PDF is empty', str(context.exception))


@apply_test_settings()
class WeasyPrintServiceTemplateTest(TestCase):
    """Test template handling functionality"""
    
    def setUp(self):
        cache.clear()
        self.cv_data = CVDataFactory.complete_cv_data()
        self.visibility = CVDataFactory.visibility_settings()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_html_success(self):
        """Test successful HTML generation"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service.template_manager, 'render_template') as mock_render:
                mock_render.return_value = "<html><body>Test</body></html>"
                
                html_content = service._generate_html(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )
                
                self.assertEqual(html_content, "<html><body>Test</body></html>")
                mock_render.assert_called_once()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_html_template_error(self):
        """Test HTML generation with template error"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service.template_manager, 'render_template') as mock_render:
                mock_render.side_effect = Exception('Template error')
                
                with self.assertRaises(PDFGenerationError) as context:
                    service._generate_html(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name='modern-0'
                    )
                
                self.assertIn('Failed to generate HTML', str(context.exception))
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_template_css_success(self):
        """Test successful CSS retrieval"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service.template_manager, 'get_template_css') as mock_css:
                mock_css.return_value = "body { font-size: 12pt; }"
                
                css_content = service._get_template_css('modern-0')
                
                self.assertEqual(css_content, "body { font-size: 12pt; }")
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_template_css_error(self):
        """Test CSS retrieval with error (should not fail)"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service.template_manager, 'get_template_css') as mock_css:
                mock_css.side_effect = Exception('CSS error')
                
                css_content = service._get_template_css('modern-0')
                
                self.assertEqual(css_content, "")  # Should return empty string
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_filter_cv_data(self):
        """Test CV data filtering based on visibility"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            visibility = {
                'personalInfo': True,
                'workExperience': False,
                'education': True,
                'skills': False
            }
            
            filtered_data = service._filter_cv_data(self.cv_data, visibility)
            
            # Should keep all sections but mark hidden ones
            self.assertIn('personalInfo', filtered_data)
            self.assertIn('workExperience', filtered_data)
            self.assertIn('education', filtered_data)
            self.assertIn('skills', filtered_data)
            
            # Check that skills section is marked as hidden or emptied
            if isinstance(filtered_data['skills'], list):
                self.assertEqual(filtered_data['skills'], [])
            elif isinstance(filtered_data['skills'], dict):
                self.assertTrue(filtered_data['skills'].get('_hidden', False))


@apply_test_settings()
class WeasyPrintServiceUtilityTest(TestCase):
    """Test utility functions"""
    
    def setUp(self):
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_suggested_filename_with_names(self):
        """Test filename generation with first and last names"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            cv_data = {
                'personalInfo': {
                    'firstName': 'John',
                    'lastName': 'Doe'
                }
            }
            
            filename = service.get_suggested_filename(cv_data)
            self.assertEqual(filename, 'John_Doe_CV.pdf')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_suggested_filename_first_name_only(self):
        """Test filename generation with first name only"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            cv_data = {
                'personalInfo': {
                    'firstName': 'Jane',
                    'lastName': ''
                }
            }
            
            filename = service.get_suggested_filename(cv_data)
            self.assertEqual(filename, 'Jane_CV.pdf')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_suggested_filename_no_names(self):
        """Test filename generation without names"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            cv_data = {
                'personalInfo': {}
            }
            
            filename = service.get_suggested_filename(cv_data)
            self.assertEqual(filename, 'CV_Resume.pdf')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_suggested_filename_special_characters(self):
        """Test filename generation with special characters"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            cv_data = {
                'personalInfo': {
                    'firstName': 'José',
                    'lastName': 'García-López'
                }
            }
            
            filename = service.get_suggested_filename(cv_data)
            # Should sanitize special characters
            self.assertRegex(filename, r'^[A-Za-z0-9_.-]+$')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_get_suggested_filename_error_handling(self):
        """Test filename generation error handling"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            # Invalid CV data that might cause errors
            cv_data = None
            
            filename = service.get_suggested_filename(cv_data)
            self.assertEqual(filename, 'CV_Resume.pdf')


@apply_test_settings()
class WeasyPrintServiceHealthTest(TestCase):
    """Test service health check functionality"""
    
    def setUp(self):
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_check_service_health_healthy(self):
        """Test health check when service is healthy"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            health = service.check_service_health()
            
            self.assertEqual(health['status'], 'healthy')
            self.assertTrue(health['weasyprint_available'])
            self.assertEqual(health['test_generation'], 'success')
            self.assertIn('test_pdf_size', health)
            self.assertIn('timestamp', health)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', False)
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_IMPORT_ERROR', 'Import error')
    def test_check_service_health_weasyprint_unavailable(self):
        """Test health check when WeasyPrint is unavailable"""
        # Can't initialize service when WeasyPrint is unavailable
        # Test the health check logic directly
        health = {
            'status': 'unhealthy',
            'weasyprint_available': False,
            'error': 'Import error',
            'timestamp': timezone.now().isoformat(),
        }
        
        self.assertEqual(health['status'], 'unhealthy')
        self.assertFalse(health['weasyprint_available'])
        self.assertEqual(health['error'], 'Import error')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_check_service_health_generation_error(self):
        """Test health check when PDF generation fails"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service, '_generate_pdf_core') as mock_core:
                mock_core.side_effect = Exception('Generation error')
                
                health = service.check_service_health()
                
                self.assertEqual(health['status'], 'degraded')
                self.assertTrue(health['weasyprint_available'])
                self.assertEqual(health['test_generation'], 'failed')
                self.assertIn('test_error', health)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    @patch('psutil.Process')
    def test_check_service_health_with_memory_info(self, mock_process_class):
        """Test health check with memory information"""
        mock_process = Mock()
        mock_process.memory_info.return_value.rss = 200 * 1024 * 1024  # 200MB
        mock_process_class.return_value = mock_process
        
        with mock_weasyprint():
            service = WeasyPrintService()
            
            health = service.check_service_health()
            
            self.assertIn('memory_usage_mb', health)
            self.assertEqual(health['memory_usage_mb'], 200.0)


@apply_test_settings()
class WeasyPrintServiceErrorHandlingTest(TestCase):
    """Test comprehensive error handling"""
    
    def setUp(self):
        cache.clear()
        self.cv_data = CVDataFactory.complete_cv_data()
        self.visibility = CVDataFactory.visibility_settings()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_weasyprint_core_error_handling(self):
        """Test error handling in WeasyPrint core generation"""
        with mock_weasyprint() as mocks:
            # Mock WeasyPrint to raise an error
            mocks['HTML'].side_effect = Exception('WeasyPrint error')
            
            service = WeasyPrintService()
            
            with self.assertRaises(PDFGenerationError) as context:
                service.generate_pdf(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )
            
            self.assertIn('WeasyPrint generation failed', str(context.exception))
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    @patch('gc.collect')
    def test_cleanup_after_error(self, mock_gc_collect):
        """Test cleanup after error"""
        with mock_weasyprint() as mocks:
            mocks['HTML'].side_effect = Exception('Test error')
            
            service = WeasyPrintService()
            
            with self.assertRaises(PDFGenerationError):
                service.generate_pdf(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )
            
            # Should trigger cleanup
            mock_gc_collect.assert_called()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_font_configuration_error_handling(self):
        """Test font configuration error handling"""
        with mock_weasyprint() as mocks:
            # Mock FontConfiguration to raise an error
            mocks['FontConfiguration'].side_effect = Exception('Font error')
            
            service = WeasyPrintService()
            
            # Should still work despite font error
            pdf_bytes = service.generate_pdf(
                cv_data=self.cv_data,
                visibility=self.visibility,
                template_name='modern-0'
            )
            
            self.assertIsInstance(pdf_bytes, bytes)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_css_error_handling(self):
        """Test CSS error handling"""
        with mock_weasyprint() as mocks:
            # Mock CSS to raise an error
            mocks['CSS'].side_effect = Exception('CSS error')
            
            service = WeasyPrintService()
            
            # Should still work despite CSS error
            pdf_bytes = service.generate_pdf(
                cv_data=self.cv_data,
                visibility=self.visibility,
                template_name='modern-0'
            )
            
            self.assertIsInstance(pdf_bytes, bytes)


@apply_test_settings()
class WeasyPrintServicePerformanceTest(TestCase):
    """Test performance and optimization features"""
    
    def setUp(self):
        cache.clear()
        self.cv_data = CVDataFactory.complete_cv_data()
        self.visibility = CVDataFactory.visibility_settings()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_concurrent_pdf_generation(self):
        """Test concurrent PDF generation"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            # Generate multiple PDFs concurrently
            import threading
            results = []
            errors = []
            
            def generate_pdf():
                try:
                    pdf_bytes = service.generate_pdf(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name='modern-0'
                    )
                    results.append(pdf_bytes)
                except Exception as e:
                    errors.append(e)
            
            threads = [threading.Thread(target=generate_pdf) for _ in range(3)]
            
            for thread in threads:
                thread.start()
            
            for thread in threads:
                thread.join()
            
            # All should succeed
            self.assertEqual(len(results), 3)
            self.assertEqual(len(errors), 0)
            
            # All results should be valid PDFs
            for pdf_bytes in results:
                self.assertIsInstance(pdf_bytes, bytes)
                self.assertGreater(len(pdf_bytes), 0)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_memory_monitoring(self):
        """Test memory monitoring during PDF generation"""
        with mock_weasyprint():
            service = WeasyPrintService()
            
            with patch.object(service.memory_manager, 'check_memory_usage') as mock_memory:
                mock_memory.return_value = 150.0  # 150MB
                
                service.generate_pdf(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )
                
                # Should check memory before and after
                self.assertGreaterEqual(mock_memory.call_count, 2)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_large_cv_data_handling(self):
        """Test handling of large CV data"""
        # Create large CV data
        large_cv_data = CVDataFactory.complete_cv_data()
        large_cv_data['workExperience'] = CVDataFactory.work_experience(20)  # 20 jobs
        large_cv_data['projects'] = CVDataFactory.projects(30)  # 30 projects
        large_cv_data['education'] = CVDataFactory.education(10)  # 10 degrees
        
        with mock_weasyprint():
            service = WeasyPrintService()
            
            pdf_bytes = service.generate_pdf(
                cv_data=large_cv_data,
                visibility=self.visibility,
                template_name='modern-0'
            )
            
            self.assertIsInstance(pdf_bytes, bytes)
            self.assertGreater(len(pdf_bytes), 0)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_service_cleanup(self):
        """Test service cleanup on destruction"""
        with mock_weasyprint():
            service = WeasyPrintService()
            pools = service.pools
            
            with patch.object(pools, 'shutdown') as mock_shutdown:
                # Trigger cleanup
                service.__del__()
                
                mock_shutdown.assert_called_once()


@apply_test_settings()
class PDFGenerationServiceTests(TestCase):
    """Test PDF generation service integration"""

    def setUp(self):
        """Set up test data"""
        if CVPDFService is None:
            self.skipTest("CVPDFService not available")

        self.pdf_service = CVPDFService()

        self.sample_cv_data = {
            'personal_info': {
                'first_name': 'John',
                'last_name': 'Doe',
                'email': '<EMAIL>',
                'phone': '+1234567890',
                'title': 'Software Engineer'
            },
            'summary': 'Experienced software engineer with 5+ years of experience.',
            'work_experience': [
                {
                    'position': 'Senior Developer',
                    'company': 'Tech Corp',
                    'location': 'San Francisco, CA',
                    'start_date': '2020-01',
                    'end_date': '2023-01',
                    'current': False,
                    'description': 'Developed web applications using Python and Django.'
                }
            ],
            'education': [
                {
                    'institution': 'University of Technology',
                    'degree': 'Bachelor of Science',
                    'field': 'Computer Science',
                    'location': 'Boston, MA',
                    'start_date': '2016-09',
                    'end_date': '2020-05',
                    'current': False
                }
            ],
            'skills': [
                {'name': 'Python', 'category': 'Programming', 'level': 9},
                {'name': 'Django', 'category': 'Frameworks', 'level': 8}
            ]
        }

        self.sample_visibility = {
            'summary': True,
            'workExperience': True,
            'education': True,
            'projects': True,
            'skills': True,
            'interests': True,
            'references': True
        }

    def test_get_suggested_filename(self):
        """Test filename generation"""
        filename = self.pdf_service.get_suggested_filename(self.sample_cv_data)
        self.assertEqual(filename, 'John_Doe_Resume.pdf')

        # Test with missing personal info
        empty_data = {}
        filename = self.pdf_service.get_suggested_filename(empty_data)
        self.assertEqual(filename, 'CV_Resume.pdf')

    def test_get_template_config(self):
        """Test template configuration retrieval"""
        config = self.pdf_service.get_template_config('classic-0')
        self.assertIn('display_name', config)
        self.assertEqual(config['name'], 'classic-0')

        # Test fallback for unknown template
        config = self.pdf_service.get_template_config('unknown-template')
        self.assertEqual(config['name'], 'unknown-template')

    @patch('apps.pdf_generation.services.render_to_string')
    def test_generate_html(self, mock_render):
        """Test HTML generation"""
        mock_render.return_value = '<html><body>Test CV</body></html>'

        html = self.pdf_service.generate_html(
            self.sample_cv_data,
            self.sample_visibility,
            'classic-0'
        )

        self.assertEqual(html, '<html><body>Test CV</body></html>')
        mock_render.assert_called_once()

        # Check that the context was passed correctly
        call_args = mock_render.call_args
        context = call_args[0][1]  # Second argument is the context
        self.assertEqual(context['cv_data'], self.sample_cv_data)
        self.assertEqual(context['visibility'], self.sample_visibility)

    @patch('apps.pdf_generation.services.HTML')
    def test_html_to_pdf_success(self, mock_html):
        """Test successful HTML to PDF conversion"""
        # Mock WeasyPrint HTML object
        mock_html_instance = MagicMock()
        mock_html.return_value = mock_html_instance
        mock_html_instance.write_pdf.return_value = None

        # Mock the PDF buffer
        with patch('io.BytesIO') as mock_bytesio:
            mock_buffer = MagicMock()
            mock_buffer.getvalue.return_value = b'fake pdf content'
            mock_bytesio.return_value = mock_buffer

            html_content = '<html><body>Test</body></html>'
            pdf_bytes = self.pdf_service.html_to_pdf(html_content, 'classic-0')

            self.assertEqual(pdf_bytes, b'fake pdf content')

    @patch('apps.pdf_generation.services.HTML')
    def test_html_to_pdf_error(self, mock_html):
        """Test HTML to PDF conversion error handling"""
        mock_html.side_effect = Exception('WeasyPrint error')

        with self.assertRaises(PDFGenerationError):
            self.pdf_service.html_to_pdf('<html></html>', 'classic-0')

    def test_get_available_templates(self):
        """Test getting available templates"""
        templates = self.pdf_service.get_available_templates()

        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)

        # Check template structure
        template = templates[0]
        required_fields = ['name', 'display_name', 'description', 'responsive', 'has_columns']
        for field in required_fields:
            self.assertIn(field, template)


@apply_test_settings()
class TemplateServiceTests(TestCase):
    """Test template service functionality"""

    def setUp(self):
        if TemplateService is None:
            self.skipTest("TemplateService not available")

    def test_get_template_list(self):
        """Test getting template list"""
        templates = TemplateService.get_template_list()

        self.assertIsInstance(templates, list)
        self.assertGreater(len(templates), 0)

    def test_validate_template(self):
        """Test template validation"""
        # Valid template
        self.assertTrue(TemplateService.validate_template('classic-0'))

        # Invalid template
        self.assertFalse(TemplateService.validate_template('nonexistent-template'))

    def test_get_template_metadata(self):
        """Test getting template metadata"""
        metadata = TemplateService.get_template_metadata('classic-0')

        self.assertIsNotNone(metadata)
        self.assertEqual(metadata['name'], 'classic-0')
        self.assertIn('display_name', metadata)

        # Test with invalid template
        metadata = TemplateService.get_template_metadata('invalid-template')
        self.assertIsNone(metadata)


@pytest.mark.django_db
class TestPDFGenerationAPI:
    """Test PDF generation API endpoints using pytest"""

    def test_template_endpoint(self, client):
        """Test templates endpoint"""
        response = client.get('/api/pdf/templates/')

        assert response.status_code == 200
        data = response.json()
        assert 'templates' in data
        assert isinstance(data['templates'], list)

    @pytest.mark.skip(reason="Requires WeasyPrint installation")
    def test_pdf_generation_endpoint(self, client, django_user_model):
        """Test PDF generation endpoint (requires WeasyPrint)"""
        # This test would require WeasyPrint to be properly installed
        # and would test the actual PDF generation endpoint
        pass
