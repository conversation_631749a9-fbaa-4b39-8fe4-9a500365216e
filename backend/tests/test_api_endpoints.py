"""
Comprehensive API Endpoint Tests

Tests all API endpoints with proper authentication and authorization:
- CV Builder API endpoints
- PDF Generation API endpoints  
- User Profile API endpoints
- Authentication endpoints
- Error handling and edge cases
"""

import uuid
import json
import time
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, MagicMock
from io import BytesIO

from django.test import TestCase, override_settings
from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth.models import User
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from apps.cv_builder.models import CVProfile, PDFGenerationLog, UserProfile, RateLimitLog
from apps.authentication.backends import SupabaseAuthentication
from .factories import (
    CVProfileFactory, PDFGenerationLogFactory, UserProfileFactory,
    SupabaseUserDataFactory, CVDataFactory, create_test_user_with_cv
)
from .mocks import (
    MockSupabaseClient, mock_supabase_service, setup_mock_supabase_user,
    mock_weasyprint, mock_celery_tasks, apply_test_settings,
    create_mock_pdf_content
)


@apply_test_settings()
class CVDataAPITest(APITestCase):
    """Test CV data management API endpoints"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'test-jwt-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    def test_get_cv_data_unauthenticated(self):
        """Test getting CV data without authentication"""
        url = reverse('cv-data')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_cv_data_no_profile(self):
        """Test getting CV data when no profile exists"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_auth.return_value.authenticate.return_value = (
                    User.objects.create_user('testuser'), self.token
                )
                
                self._authenticate()
                url = reverse('cv-data')
                response = self.client.get(url)
                
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                self.assertEqual(response.data, {})  # Empty data for new user
    
    def test_get_cv_data_existing_profile(self):
        """Test getting CV data with existing profile"""
        cv_data = CVDataFactory.complete_cv_data()
        cv_profile = CVProfileFactory(
            user_id=self.user_data['id'],
            cv_content=cv_data
        )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    mock_cv_objects.get.return_value = cv_profile
                    
                    response = self.client.get(url)
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertEqual(response.data, cv_data)
    
    def test_update_cv_data(self):
        """Test updating CV data"""
        cv_data = CVDataFactory.complete_cv_data()
        updated_data = cv_data.copy()
        updated_data['personalInfo']['firstName'] = 'Updated Name'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                response = self.client.put(
                    url,
                    data=updated_data,
                    format='json'
                )
                
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                self.assertIn('success', response.data)
    
    def test_update_cv_data_invalid_json(self):
        """Test updating CV data with invalid JSON"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                response = self.client.put(
                    url,
                    data="invalid json",
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_update_cv_data_missing_fields(self):
        """Test updating CV data with missing required fields"""
        incomplete_data = {
            'personalInfo': {
                'firstName': 'John'
                # Missing other required fields
            }
        }
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                response = self.client.put(
                    url,
                    data=incomplete_data,
                    format='json'
                )
                
                # Should still accept partial data
                self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_cv_data_template_selection(self):
        """Test CV data template selection"""
        cv_data = CVDataFactory.complete_cv_data()
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                # Include template in request
                request_data = {
                    'cv_content': cv_data,
                    'template_name': 'modern-0'
                }
                
                response = self.client.put(
                    url,
                    data=request_data,
                    format='json'
                )
                
                self.assertEqual(response.status_code, status.HTTP_200_OK)


@apply_test_settings()
class PDFGenerationAPITest(APITestCase):
    """Test PDF generation API endpoints"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'test-jwt-token'
        self.cv_data = CVDataFactory.complete_cv_data()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_sync_success(self):
        """Test synchronous PDF generation success"""
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user('testuser')
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    url = reverse('generate-pdf')
                    
                    request_data = {
                        'cv_data': self.cv_data,
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertEqual(response['Content-Type'], 'application/pdf')
                    self.assertGreater(len(response.content), 0)
    
    def test_generate_pdf_unauthenticated(self):
        """Test PDF generation without authentication"""
        url = reverse('generate-pdf')
        response = self.client.post(url, data={})
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_missing_data(self):
        """Test PDF generation with missing required data"""
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user('testuser')
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    url = reverse('generate-pdf')
                    
                    # Missing cv_data
                    request_data = {
                        'template_name': 'modern-0'
                    }
                    
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    
                    self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_generate_pdf_invalid_template(self):
        """Test PDF generation with invalid template"""
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user('testuser')
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    url = reverse('generate-pdf')
                    
                    request_data = {
                        'cv_data': self.cv_data,
                        'template_name': 'nonexistent-template',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    
                    self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_generate_pdf_async_success(self):
        """Test asynchronous PDF generation success"""
        with mock_celery_tasks() as mock_tasks:
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user('testuser')
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    url = reverse('generate-pdf-async')
                    
                    request_data = {
                        'cv_data': self.cv_data,
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    
                    self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
                    self.assertIn('task_id', response.data)
                    self.assertIn('status', response.data)
    
    def test_pdf_generation_status_check(self):
        """Test PDF generation status checking"""
        pdf_gen = PDFGenerationLogFactory(
            user_id=self.user_data['id'],
            status='processing',
            task_id='test-task-123'
        )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('pdf-status', kwargs={'task_id': 'test-task-123'})
                
                with patch('apps.pdf_generation.views.PDFGenerationLog.objects') as mock_pdf_objects:
                    mock_pdf_objects.get.return_value = pdf_gen
                    
                    response = self.client.get(url)
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertEqual(response.data['status'], 'processing')
                    self.assertEqual(response.data['task_id'], 'test-task-123')
    
    def test_pdf_generation_rate_limiting(self):
        """Test PDF generation rate limiting"""
        # Create rate limit logs to simulate reaching limit
        now = timezone.now()
        for i in range(5):  # Assuming 5 PDFs per hour limit
            RateLimitLog.objects.create(
                user_id=self.user_data['id'],
                action='pdf_generation',
                created_at=now - timedelta(minutes=i * 10)
            )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('generate-pdf')
                
                request_data = {
                    'cv_data': self.cv_data,
                    'template_name': 'modern-0',
                    'visibility': CVDataFactory.visibility_settings()
                }
                
                with patch('apps.pdf_generation.views.check_rate_limit') as mock_rate_limit:
                    mock_rate_limit.return_value = False  # Rate limit exceeded
                    
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    
                    self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)


@apply_test_settings()
class AuthenticationAPITest(APITestCase):
    """Test authentication-related API functionality"""
    
    def setUp(self):
        self.client = APIClient()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_protected_endpoint_without_token(self):
        """Test accessing protected endpoint without token"""
        url = reverse('cv-data')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_protected_endpoint_invalid_token(self):
        """Test accessing protected endpoint with invalid token"""
        self.client.credentials(HTTP_AUTHORIZATION='Bearer invalid-token')
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, 'invalid-token', None)
            
            url = reverse('cv-data')
            response = self.client.get(url)
            
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_protected_endpoint_expired_token(self):
        """Test accessing protected endpoint with expired token"""
        expired_token = 'expired.jwt.token'
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {expired_token}')
        
        with mock_supabase_service() as (service, mock_client):
            # Mock expired token response
            mock_client.auth.get_user.return_value.user = None
            
            url = reverse('cv-data')
            response = self.client.get(url)
            
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_token_refresh_scenario(self):
        """Test token refresh scenario"""
        user_data = SupabaseUserDataFactory.create_user_data()
        old_token = 'old-token'
        new_token = 'new-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, new_token, user_data)
            
            # First request with old token fails
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {old_token}')
            setup_mock_supabase_user(mock_client, old_token, None)
            
            url = reverse('cv-data')
            response = self.client.get(url)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
            
            # Second request with new token succeeds
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {new_token}')
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, new_token)
                
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_user_context_in_requests(self):
        """Test that user context is properly set in requests"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'context-test-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, token)
                
                # Mock the view to capture request context
                with patch('apps.cv_builder.views.CVDataView.get') as mock_get:
                    def capture_request(request):
                        # Verify request has user context
                        self.assertEqual(request.user, mock_user)
                        self.assertEqual(getattr(request, 'user_id', None), user_data['id'])
                        return Mock(status_code=200, data={})
                    
                    mock_get.side_effect = capture_request
                    
                    url = reverse('cv-data')
                    response = self.client.get(url)


@apply_test_settings()
class APIErrorHandlingTest(APITestCase):
    """Test API error handling scenarios"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'error-test-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    def test_database_error_handling(self):
        """Test API handling of database errors"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    mock_cv_objects.get.side_effect = Exception('Database error')
                    
                    url = reverse('cv-data')
                    response = self.client.get(url)
                    
                    self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def test_validation_error_handling(self):
        """Test API handling of validation errors"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                # Send invalid data
                invalid_data = {
                    'personalInfo': {
                        'email': 'invalid-email'  # Invalid email format
                    }
                }
                
                response = self.client.put(
                    url,
                    data=invalid_data,
                    format='json'
                )
                
                # Should handle validation gracefully
                self.assertIn(response.status_code, [
                    status.HTTP_400_BAD_REQUEST,
                    status.HTTP_200_OK  # Might accept invalid data but sanitize
                ])
    
    def test_pdf_generation_error_handling(self):
        """Test PDF generation error handling"""
        with mock_weasyprint() as mocks:
            # Mock WeasyPrint to raise an error
            mocks['HTML'].side_effect = Exception('PDF generation failed')
            
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user('testuser')
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    url = reverse('generate-pdf')
                    
                    request_data = {
                        'cv_data': CVDataFactory.complete_cv_data(),
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    
                    self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def test_malformed_json_handling(self):
        """Test handling of malformed JSON requests"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                # Send malformed JSON
                response = self.client.put(
                    url,
                    data='{"invalid": json}',
                    content_type='application/json'
                )
                
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_large_payload_handling(self):
        """Test handling of large request payloads"""
        # Create very large CV data
        large_cv_data = CVDataFactory.complete_cv_data()
        large_cv_data['workExperience'] = CVDataFactory.work_experience(100)  # 100 jobs
        large_cv_data['projects'] = CVDataFactory.projects(200)  # 200 projects
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                response = self.client.put(
                    url,
                    data=large_cv_data,
                    format='json'
                )
                
                # Should handle large payloads (or reject them appropriately)
                self.assertIn(response.status_code, [
                    status.HTTP_200_OK,
                    status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                    status.HTTP_400_BAD_REQUEST
                ])


@apply_test_settings()
class APIPerformanceTest(APITestCase):
    """Test API performance characteristics"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'performance-test-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    def test_cv_data_response_time(self):
        """Test CV data API response time"""
        cv_profile = CVProfileFactory(
            user_id=self.user_data['id'],
            cv_content=CVDataFactory.complete_cv_data()
        )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    mock_cv_objects.get.return_value = cv_profile
                    
                    start_time = time.time()
                    response = self.client.get(url)
                    response_time = time.time() - start_time
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertLess(response_time, 1.0)  # Should respond within 1 second
    
    def test_concurrent_api_requests(self):
        """Test handling of concurrent API requests"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                # Make multiple concurrent requests
                import threading
                results = []
                errors = []
                
                def make_request():
                    try:
                        response = self.client.get(url)
                        results.append(response.status_code)
                    except Exception as e:
                        errors.append(e)
                
                threads = [threading.Thread(target=make_request) for _ in range(10)]
                
                start_time = time.time()
                for thread in threads:
                    thread.start()
                
                for thread in threads:
                    thread.join()
                
                total_time = time.time() - start_time
                
                # All requests should succeed
                self.assertEqual(len(errors), 0)
                self.assertEqual(len(results), 10)
                
                # Should handle concurrent requests efficiently
                self.assertLess(total_time, 5.0)  # 5 seconds for 10 concurrent requests
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_response_time(self):
        """Test PDF generation response time"""
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user('testuser')
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    url = reverse('generate-pdf')
                    
                    request_data = {
                        'cv_data': CVDataFactory.complete_cv_data(),
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    start_time = time.time()
                    response = self.client.post(
                        url,
                        data=request_data,
                        format='json'
                    )
                    response_time = time.time() - start_time
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertLess(response_time, 10.0)  # Should generate within 10 seconds
    
    def test_api_caching_behavior(self):
        """Test API caching behavior"""
        cv_profile = CVProfileFactory(
            user_id=self.user_data['id'],
            cv_content=CVDataFactory.complete_cv_data()
        )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user('testuser')
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                url = reverse('cv-data')
                
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    mock_cv_objects.get.return_value = cv_profile
                    
                    # First request
                    start_time = time.time()
                    response1 = self.client.get(url)
                    first_time = time.time() - start_time
                    
                    # Second request (might be cached)
                    start_time = time.time()
                    response2 = self.client.get(url)
                    second_time = time.time() - start_time
                    
                    self.assertEqual(response1.status_code, status.HTTP_200_OK)
                    self.assertEqual(response2.status_code, status.HTTP_200_OK)
                    self.assertEqual(response1.data, response2.data)
                    
                    # Second request should be faster if cached
                    # (This is optional depending on caching implementation)
                    # self.assertLess(second_time, first_time)
