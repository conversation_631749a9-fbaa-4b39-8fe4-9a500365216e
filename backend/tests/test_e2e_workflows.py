"""
End-to-End Workflow Tests for CVFlo

Tests complete user workflows from authentication to PDF generation including:
- User registration and authentication flows
- CV creation and editing workflows
- PDF generation and download workflows
- Error recovery and edge case handling
- Multi-user scenarios and data isolation
"""

import uuid
import json
import time
from datetime import datetime, timedelta
from unittest.mock import patch, Mock, MagicMock

from django.test import TestCase, TransactionTestCase, override_settings
from django.core.cache import cache
from django.utils import timezone
from django.contrib.auth.models import User
from django.db import transaction
from rest_framework.test import APITestCase, APIClient
from rest_framework import status

from apps.cv_builder.models import CVProfile, PDFGenerationLog, UserProfile, RateLimitLog
from .factories import (
    CVProfileFactory, PDFGenerationLogFactory, UserProfileFactory,
    SupabaseUserDataFactory, CVDataFactory
)
from .mocks import (
    mock_supabase_service, mock_weasyprint, mock_celery_tasks,
    setup_mock_supabase_user, apply_test_settings
)


@apply_test_settings()
class UserRegistrationWorkflowTest(APITestCase):
    """Test complete user registration and onboarding workflow"""
    
    def setUp(self):
        self.client = APIClient()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_new_user_registration_workflow(self):
        """Test complete new user registration workflow"""
        # Step 1: New user signs up (this happens in Supabase)
        user_data = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>',
            email_verified=True,
            first_name='John',
            last_name='Doe'
        )
        token = 'new-user-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            # Step 2: First API request creates Django user and profile
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=user_data['id'],
                    email=user_data['email'],
                    first_name=user_data['user_metadata']['first_name'],
                    last_name=user_data['user_metadata']['last_name']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, token)
                
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                
                # Step 3: User accesses CV data (empty for new user)
                from django.urls import reverse
                url = reverse('cv-data')
                response = self.client.get(url)
                
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                self.assertEqual(response.data, {})
                
                # Step 4: Verify UserProfile was created
                try:
                    user_profile = UserProfile.objects.get(id=user_data['id'])
                    self.assertEqual(user_profile.email, user_data['email'])
                except UserProfile.DoesNotExist:
                    # UserProfile creation might be handled differently
                    pass
    
    def test_unverified_user_registration_workflow(self):
        """Test registration workflow for unverified email"""
        # Step 1: User signs up but email is not verified
        user_data = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>',
            email_verified=False
        )
        token = 'unverified-user-token'
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            # Step 2: Authentication should fail for unverified user
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                from rest_framework.exceptions import AuthenticationFailed
                mock_auth.return_value.authenticate.side_effect = AuthenticationFailed('User account is inactive')
                
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                
                from django.urls import reverse
                url = reverse('cv-data')
                response = self.client.get(url)
                
                self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_existing_user_login_workflow(self):
        """Test workflow for existing user login"""
        # Step 1: Create existing user data
        user_data = SupabaseUserDataFactory.create_user_data(
            email='<EMAIL>'
        )
        token = 'existing-user-token'
        
        # Pre-create user and CV profile
        existing_cv_data = CVDataFactory.complete_cv_data()
        cv_profile = CVProfileFactory(
            user_id=user_data['id'],
            cv_content=existing_cv_data
        )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, token, user_data)
            
            # Step 2: User logs in and accesses their data
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=user_data['id'],
                    email=user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, token)
                
                self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')
                
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    mock_cv_objects.get.return_value = cv_profile
                    
                    from django.urls import reverse
                    url = reverse('cv-data')
                    response = self.client.get(url)
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertEqual(response.data, existing_cv_data)


@apply_test_settings()
class CVCreationWorkflowTest(APITestCase):
    """Test CV creation and editing workflows"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'cv-workflow-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    def test_complete_cv_creation_workflow(self):
        """Test complete CV creation from scratch"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                # Step 1: Start with empty CV data
                from django.urls import reverse
                url = reverse('cv-data')
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 2: Add personal information
                personal_info = {
                    'personalInfo': {
                        'firstName': 'John',
                        'lastName': 'Doe',
                        'email': '<EMAIL>',
                        'phone': '+1234567890',
                        'location': 'New York, NY',
                        'summary': 'Experienced software developer'
                    }
                }
                
                response = self.client.put(url, data=personal_info, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 3: Add work experience
                work_experience_data = personal_info.copy()
                work_experience_data['workExperience'] = [
                    {
                        'company': 'Tech Corp',
                        'position': 'Software Developer',
                        'location': 'New York, NY',
                        'startDate': '2020-01-01',
                        'endDate': '2023-12-31',
                        'current': False,
                        'description': 'Developed web applications',
                        'achievements': ['Increased performance by 50%']
                    }
                ]
                
                response = self.client.put(url, data=work_experience_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 4: Add education
                complete_cv_data = work_experience_data.copy()
                complete_cv_data['education'] = [
                    {
                        'institution': 'University of Technology',
                        'degree': 'Bachelor of Science',
                        'field': 'Computer Science',
                        'location': 'New York, NY',
                        'graduationDate': '2019-05-15',
                        'gpa': '3.8',
                        'honors': 'Cum Laude'
                    }
                ]
                
                response = self.client.put(url, data=complete_cv_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 5: Add skills
                complete_cv_data['skills'] = {
                    'technical': ['Python', 'JavaScript', 'React', 'Django'],
                    'soft': ['Leadership', 'Communication', 'Problem Solving'],
                    'languages': ['English (Native)', 'Spanish (Intermediate)']
                }
                
                response = self.client.put(url, data=complete_cv_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 6: Verify complete CV data can be retrieved
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                self.assertIn('personalInfo', response.data)
                self.assertIn('workExperience', response.data)
                self.assertIn('education', response.data)
                self.assertIn('skills', response.data)
    
    def test_cv_editing_workflow(self):
        """Test CV editing and updating workflow"""
        # Create initial CV data
        initial_cv_data = CVDataFactory.complete_cv_data()
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                # Step 1: Set initial CV data
                from django.urls import reverse
                url = reverse('cv-data')
                response = self.client.put(url, data=initial_cv_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 2: Edit personal information
                updated_cv_data = initial_cv_data.copy()
                updated_cv_data['personalInfo']['firstName'] = 'Updated Name'
                updated_cv_data['personalInfo']['summary'] = 'Updated summary'
                
                response = self.client.put(url, data=updated_cv_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 3: Add new work experience
                new_job = {
                    'company': 'New Company',
                    'position': 'Senior Developer',
                    'location': 'San Francisco, CA',
                    'startDate': '2024-01-01',
                    'endDate': None,
                    'current': True,
                    'description': 'Leading development team',
                    'achievements': ['Implemented new architecture']
                }
                updated_cv_data['workExperience'].append(new_job)
                
                response = self.client.put(url, data=updated_cv_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 4: Remove an education entry
                if updated_cv_data.get('education'):
                    updated_cv_data['education'] = updated_cv_data['education'][:-1]
                    
                    response = self.client.put(url, data=updated_cv_data, format='json')
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 5: Verify all changes are persisted
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                self.assertEqual(response.data['personalInfo']['firstName'], 'Updated Name')
                self.assertEqual(len(response.data['workExperience']), len(initial_cv_data['workExperience']) + 1)
    
    def test_cv_template_selection_workflow(self):
        """Test CV template selection workflow"""
        cv_data = CVDataFactory.complete_cv_data()
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                # Step 1: Create CV with default template
                from django.urls import reverse
                url = reverse('cv-data')
                response = self.client.put(url, data=cv_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 2: Change template
                templates_to_test = ['classic-0', 'modern-0', 'modern-1', 'academic-0']
                
                for template in templates_to_test:
                    template_data = {
                        'cv_content': cv_data,
                        'template_name': template
                    }
                    
                    response = self.client.put(url, data=template_data, format='json')
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    
                    # Verify template was saved (this would depend on implementation)
                    # In a real scenario, you might have a separate endpoint for templates
    
    def test_cv_validation_workflow(self):
        """Test CV data validation during creation/editing"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                # Step 1: Test with invalid email format
                invalid_data = {
                    'personalInfo': {
                        'firstName': 'John',
                        'lastName': 'Doe',
                        'email': 'invalid-email-format',  # Invalid email
                        'phone': 'invalid-phone'  # Invalid phone
                    }
                }
                
                from django.urls import reverse
                url = reverse('cv-data')
                response = self.client.put(url, data=invalid_data, format='json')
                
                # The response depends on validation implementation
                # Could be 400 (validation error) or 200 (accepted but sanitized)
                self.assertIn(response.status_code, [status.HTTP_200_OK, status.HTTP_400_BAD_REQUEST])
                
                # Step 2: Test with missing required fields
                incomplete_data = {
                    'personalInfo': {
                        'firstName': 'John'
                        # Missing other fields
                    }
                }
                
                response = self.client.put(url, data=incomplete_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)  # Should accept partial data
                
                # Step 3: Test with valid data
                valid_data = {
                    'personalInfo': {
                        'firstName': 'John',
                        'lastName': 'Doe',
                        'email': '<EMAIL>',
                        'phone': '+1234567890'
                    }
                }
                
                response = self.client.put(url, data=valid_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_200_OK)


@apply_test_settings()
class PDFGenerationWorkflowTest(APITestCase):
    """Test PDF generation workflows"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'pdf-workflow-token'
        self.cv_data = CVDataFactory.complete_cv_data()
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_synchronous_pdf_generation_workflow(self):
        """Test synchronous PDF generation workflow"""
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user(
                        username=self.user_data['id'],
                        email=self.user_data['email']
                    )
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    
                    # Step 1: Generate PDF with complete CV data
                    from django.urls import reverse
                    url = reverse('generate-pdf')
                    
                    request_data = {
                        'cv_data': self.cv_data,
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    response = self.client.post(url, data=request_data, format='json')
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertEqual(response['Content-Type'], 'application/pdf')
                    self.assertGreater(len(response.content), 0)
                    
                    # Step 2: Verify PDF content is valid
                    pdf_content = response.content
                    self.assertTrue(pdf_content.startswith(b'%PDF'))
                    self.assertTrue(pdf_content.endswith(b'%%EOF'))
    
    def test_asynchronous_pdf_generation_workflow(self):
        """Test asynchronous PDF generation workflow"""
        with mock_celery_tasks() as mock_tasks:
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user(
                        username=self.user_data['id'],
                        email=self.user_data['email']
                    )
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    
                    # Step 1: Submit PDF generation request
                    from django.urls import reverse
                    url = reverse('generate-pdf-async')
                    
                    request_data = {
                        'cv_data': self.cv_data,
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    response = self.client.post(url, data=request_data, format='json')
                    
                    self.assertEqual(response.status_code, status.HTTP_202_ACCEPTED)
                    self.assertIn('task_id', response.data)
                    
                    task_id = response.data['task_id']
                    
                    # Step 2: Check generation status
                    status_url = reverse('pdf-status', kwargs={'task_id': task_id})
                    
                    # Mock the PDF generation log
                    with patch('apps.pdf_generation.views.PDFGenerationLog.objects') as mock_pdf_objects:
                        mock_log = Mock()
                        mock_log.status = 'processing'
                        mock_log.task_id = task_id
                        mock_log.created_at = timezone.now()
                        mock_pdf_objects.get.return_value = mock_log
                        
                        response = self.client.get(status_url)
                        
                        self.assertEqual(response.status_code, status.HTTP_200_OK)
                        self.assertEqual(response.data['status'], 'processing')
                        self.assertEqual(response.data['task_id'], task_id)
                    
                    # Step 3: Simulate completion
                    with patch('apps.pdf_generation.views.PDFGenerationLog.objects') as mock_pdf_objects:
                        mock_log.status = 'completed'
                        mock_log.file_url = 'https://storage.example.com/test.pdf'
                        mock_log.file_size = 50000
                        mock_pdf_objects.get.return_value = mock_log
                        
                        response = self.client.get(status_url)
                        
                        self.assertEqual(response.status_code, status.HTTP_200_OK)
                        self.assertEqual(response.data['status'], 'completed')
                        self.assertIn('file_url', response.data)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_with_different_templates(self):
        """Test PDF generation with different templates"""
        templates = ['classic-0', 'modern-0', 'modern-1', 'academic-0']
        
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user(
                        username=self.user_data['id'],
                        email=self.user_data['email']
                    )
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    
                    from django.urls import reverse
                    url = reverse('generate-pdf')
                    
                    for template in templates:
                        request_data = {
                            'cv_data': self.cv_data,
                            'template_name': template,
                            'visibility': CVDataFactory.visibility_settings()
                        }
                        
                        response = self.client.post(url, data=request_data, format='json')
                        
                        self.assertEqual(response.status_code, status.HTTP_200_OK, 
                                       f"Template {template} failed")
                        self.assertEqual(response['Content-Type'], 'application/pdf')
                        self.assertGreater(len(response.content), 0)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_with_visibility_settings(self):
        """Test PDF generation with different visibility settings"""
        visibility_scenarios = [
            {'personalInfo': True, 'workExperience': True, 'education': True, 'skills': True},
            {'personalInfo': True, 'workExperience': True, 'education': False, 'skills': False},
            {'personalInfo': True, 'workExperience': False, 'education': True, 'skills': True},
        ]
        
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token, self.user_data)
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user = User.objects.create_user(
                        username=self.user_data['id'],
                        email=self.user_data['email']
                    )
                    mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                    
                    self._authenticate()
                    
                    from django.urls import reverse
                    url = reverse('generate-pdf')
                    
                    for i, visibility in enumerate(visibility_scenarios):
                        request_data = {
                            'cv_data': self.cv_data,
                            'template_name': 'modern-0',
                            'visibility': visibility
                        }
                        
                        response = self.client.post(url, data=request_data, format='json')
                        
                        self.assertEqual(response.status_code, status.HTTP_200_OK,
                                       f"Visibility scenario {i} failed")
                        self.assertEqual(response['Content-Type'], 'application/pdf')
                        self.assertGreater(len(response.content), 0)
    
    def test_pdf_generation_error_handling_workflow(self):
        """Test PDF generation error handling workflow"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                from django.urls import reverse
                url = reverse('generate-pdf')
                
                # Step 1: Test with missing CV data
                request_data = {
                    'template_name': 'modern-0',
                    'visibility': CVDataFactory.visibility_settings()
                    # Missing cv_data
                }
                
                response = self.client.post(url, data=request_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                
                # Step 2: Test with invalid template
                request_data = {
                    'cv_data': self.cv_data,
                    'template_name': 'nonexistent-template',
                    'visibility': CVDataFactory.visibility_settings()
                }
                
                response = self.client.post(url, data=request_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
                
                # Step 3: Test with malformed CV data
                request_data = {
                    'cv_data': 'invalid-data-format',
                    'template_name': 'modern-0',
                    'visibility': CVDataFactory.visibility_settings()
                }
                
                response = self.client.post(url, data=request_data, format='json')
                self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


@apply_test_settings()
class RateLimitingWorkflowTest(APITestCase):
    """Test rate limiting workflows"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'rate-limit-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    def test_pdf_generation_rate_limiting_workflow(self):
        """Test PDF generation rate limiting workflow"""
        # Create rate limit logs to simulate reaching limit
        now = timezone.now()
        for i in range(5):  # Create 5 recent PDF generations
            RateLimitLog.objects.create(
                user_id=self.user_data['id'],
                action='pdf_generation',
                ip_address='*************',
                created_at=now - timedelta(minutes=i * 10)
            )
        
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                # Step 1: Try to generate PDF when rate limited
                from django.urls import reverse
                url = reverse('generate-pdf')
                
                request_data = {
                    'cv_data': CVDataFactory.complete_cv_data(),
                    'template_name': 'modern-0',
                    'visibility': CVDataFactory.visibility_settings()
                }
                
                with patch('apps.pdf_generation.views.check_rate_limit') as mock_rate_limit:
                    mock_rate_limit.return_value = False  # Rate limit exceeded
                    
                    response = self.client.post(url, data=request_data, format='json')
                    
                    self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
                    self.assertIn('rate limit', response.data.get('error', '').lower())
                
                # Step 2: Wait for rate limit to reset (simulated)
                # Clear old rate limit logs
                RateLimitLog.objects.filter(
                    user_id=self.user_data['id'],
                    created_at__lt=now - timedelta(hours=1)
                ).delete()
                
                with patch('apps.pdf_generation.views.check_rate_limit') as mock_rate_limit:
                    mock_rate_limit.return_value = True  # Rate limit OK
                    
                    with mock_weasyprint():
                        response = self.client.post(url, data=request_data, format='json')
                        
                        self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_rate_limit_bypass_attempt_workflow(self):
        """Test workflow for rate limit bypass attempts"""
        # This would test scenarios where users try to bypass rate limits
        # Such as changing IP addresses, using multiple accounts, etc.
        
        # Create multiple rate limit logs from different IPs for same user
        now = timezone.now()
        ips = ['*************', '*************', '********']
        
        for ip in ips:
            for i in range(2):
                RateLimitLog.objects.create(
                    user_id=self.user_data['id'],
                    action='pdf_generation',
                    ip_address=ip,
                    created_at=now - timedelta(minutes=i * 5)
                )
        
        # Total: 6 requests from same user, but different IPs
        total_requests = RateLimitLog.objects.filter(
            user_id=self.user_data['id'],
            action='pdf_generation',
            created_at__gte=now - timedelta(hours=1)
        ).count()
        
        self.assertEqual(total_requests, 6)
        
        # Rate limiting should be based on user_id, not just IP
        # So all requests should count towards the user's limit


@apply_test_settings()
class MultiUserWorkflowTest(APITestCase):
    """Test multi-user scenarios and data isolation"""
    
    def setUp(self):
        self.client = APIClient()
        self.user1_data = SupabaseUserDataFactory.create_user_data(email='<EMAIL>')
        self.user2_data = SupabaseUserDataFactory.create_user_data(email='<EMAIL>')
        self.token1 = 'user1-token'
        self.token2 = 'user2-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def test_concurrent_user_workflow(self):
        """Test concurrent operations by multiple users"""
        with mock_supabase_service() as (service, mock_client):
            # Setup both users
            setup_mock_supabase_user(mock_client, self.token1, self.user1_data)
            setup_mock_supabase_user(mock_client, self.token2, self.user2_data)
            
            # Create separate clients for each user
            client1 = APIClient()
            client2 = APIClient()
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user1 = User.objects.create_user(
                    username=self.user1_data['id'],
                    email=self.user1_data['email']
                )
                mock_user2 = User.objects.create_user(
                    username=self.user2_data['id'],
                    email=self.user2_data['email']
                )
                
                def auth_side_effect(request):
                    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
                    if self.token1 in auth_header:
                        return (mock_user1, self.token1)
                    elif self.token2 in auth_header:
                        return (mock_user2, self.token2)
                    return None
                
                mock_auth.return_value.authenticate.side_effect = auth_side_effect
                
                # Authenticate both clients
                client1.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token1}')
                client2.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token2}')
                
                from django.urls import reverse
                url = reverse('cv-data')
                
                # User 1 creates CV data
                user1_cv_data = CVDataFactory.complete_cv_data()
                user1_cv_data['personalInfo']['firstName'] = 'User One'
                
                response1 = client1.put(url, data=user1_cv_data, format='json')
                self.assertEqual(response1.status_code, status.HTTP_200_OK)
                
                # User 2 creates different CV data
                user2_cv_data = CVDataFactory.complete_cv_data()
                user2_cv_data['personalInfo']['firstName'] = 'User Two'
                
                response2 = client2.put(url, data=user2_cv_data, format='json')
                self.assertEqual(response2.status_code, status.HTTP_200_OK)
                
                # Verify data isolation - each user should only see their own data
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    def get_side_effect(*args, **kwargs):
                        user_id = kwargs.get('user_id')
                        if user_id == self.user1_data['id']:
                            mock_profile = Mock()
                            mock_profile.cv_content = user1_cv_data
                            return mock_profile
                        elif user_id == self.user2_data['id']:
                            mock_profile = Mock()
                            mock_profile.cv_content = user2_cv_data
                            return mock_profile
                        raise CVProfile.DoesNotExist
                    
                    mock_cv_objects.get.side_effect = get_side_effect
                    
                    # User 1 retrieves their data
                    response1 = client1.get(url)
                    self.assertEqual(response1.status_code, status.HTTP_200_OK)
                    self.assertEqual(response1.data['personalInfo']['firstName'], 'User One')
                    
                    # User 2 retrieves their data
                    response2 = client2.get(url)
                    self.assertEqual(response2.status_code, status.HTTP_200_OK)
                    self.assertEqual(response2.data['personalInfo']['firstName'], 'User Two')
    
    def test_user_data_isolation_workflow(self):
        """Test that users cannot access each other's data"""
        # Create CV profiles for both users
        cv_profile1 = CVProfileFactory(
            user_id=self.user1_data['id'],
            cv_content={'personalInfo': {'firstName': 'User One'}}
        )
        cv_profile2 = CVProfileFactory(
            user_id=self.user2_data['id'],
            cv_content={'personalInfo': {'firstName': 'User Two'}}
        )
        
        # Verify data isolation at database level
        user1_profiles = CVProfile.objects.filter(user_id=self.user1_data['id'])
        user2_profiles = CVProfile.objects.filter(user_id=self.user2_data['id'])
        
        self.assertEqual(user1_profiles.count(), 1)
        self.assertEqual(user2_profiles.count(), 1)
        self.assertEqual(user1_profiles.first().id, cv_profile1.id)
        self.assertEqual(user2_profiles.first().id, cv_profile2.id)
        
        # Cross-user queries should return nothing
        cross_query = CVProfile.objects.filter(
            user_id=self.user1_data['id'],
            id=cv_profile2.id
        )
        self.assertEqual(cross_query.count(), 0)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_concurrent_pdf_generation_workflow(self):
        """Test concurrent PDF generation by multiple users"""
        with mock_weasyprint():
            with mock_supabase_service() as (service, mock_client):
                setup_mock_supabase_user(mock_client, self.token1, self.user1_data)
                setup_mock_supabase_user(mock_client, self.token2, self.user2_data)
                
                client1 = APIClient()
                client2 = APIClient()
                
                with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                    mock_user1 = User.objects.create_user(
                        username=self.user1_data['id'],
                        email=self.user1_data['email']
                    )
                    mock_user2 = User.objects.create_user(
                        username=self.user2_data['id'],
                        email=self.user2_data['email']
                    )
                    
                    def auth_side_effect(request):
                        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
                        if self.token1 in auth_header:
                            return (mock_user1, self.token1)
                        elif self.token2 in auth_header:
                            return (mock_user2, self.token2)
                        return None
                    
                    mock_auth.return_value.authenticate.side_effect = auth_side_effect
                    
                    client1.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token1}')
                    client2.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token2}')
                    
                    from django.urls import reverse
                    url = reverse('generate-pdf')
                    
                    # Both users generate PDFs simultaneously
                    request_data1 = {
                        'cv_data': CVDataFactory.complete_cv_data(),
                        'template_name': 'modern-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    request_data2 = {
                        'cv_data': CVDataFactory.complete_cv_data(),
                        'template_name': 'classic-0',
                        'visibility': CVDataFactory.visibility_settings()
                    }
                    
                    # Generate PDFs concurrently
                    import threading
                    results = []
                    errors = []
                    
                    def generate_pdf1():
                        try:
                            response = client1.post(url, data=request_data1, format='json')
                            results.append(('user1', response.status_code, len(response.content)))
                        except Exception as e:
                            errors.append(('user1', e))
                    
                    def generate_pdf2():
                        try:
                            response = client2.post(url, data=request_data2, format='json')
                            results.append(('user2', response.status_code, len(response.content)))
                        except Exception as e:
                            errors.append(('user2', e))
                    
                    thread1 = threading.Thread(target=generate_pdf1)
                    thread2 = threading.Thread(target=generate_pdf2)
                    
                    thread1.start()
                    thread2.start()
                    
                    thread1.join()
                    thread2.join()
                    
                    # Both generations should succeed
                    self.assertEqual(len(errors), 0)
                    self.assertEqual(len(results), 2)
                    
                    for user, status_code, content_length in results:
                        self.assertEqual(status_code, status.HTTP_200_OK)
                        self.assertGreater(content_length, 0)


@apply_test_settings()
class ErrorRecoveryWorkflowTest(APITestCase):
    """Test error recovery and resilience workflows"""
    
    def setUp(self):
        self.client = APIClient()
        self.user_data = SupabaseUserDataFactory.create_user_data()
        self.token = 'error-recovery-token'
        cache.clear()
    
    def tearDown(self):
        cache.clear()
    
    def _authenticate(self):
        """Helper method to authenticate requests"""
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')
    
    def test_database_error_recovery_workflow(self):
        """Test database error recovery workflow"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                from django.urls import reverse
                url = reverse('cv-data')
                
                # Step 1: Simulate database error
                with patch('apps.cv_builder.views.CVProfile.objects') as mock_cv_objects:
                    mock_cv_objects.get.side_effect = Exception('Database connection lost')
                    
                    response = self.client.get(url)
                    self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
                
                # Step 2: Database recovery (error no longer occurs)
                cv_data = CVDataFactory.complete_cv_data()
                response = self.client.put(url, data=cv_data, format='json')
                
                # Should work normally after recovery
                self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    def test_authentication_error_recovery_workflow(self):
        """Test authentication error recovery workflow"""
        with mock_supabase_service() as (service, mock_client):
            # Step 1: Authentication fails
            setup_mock_supabase_user(mock_client, self.token, None)  # No user data
            
            self._authenticate()
            
            from django.urls import reverse
            url = reverse('cv-data')
            response = self.client.get(url)
            
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
            
            # Step 2: Authentication recovers (token becomes valid)
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                # Clear cache to force re-authentication
                cache.clear()
                
                response = self.client.get(url)
                self.assertEqual(response.status_code, status.HTTP_200_OK)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_error_recovery_workflow(self):
        """Test PDF generation error recovery workflow"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.pdf_generation.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                from django.urls import reverse
                url = reverse('generate-pdf')
                
                request_data = {
                    'cv_data': CVDataFactory.complete_cv_data(),
                    'template_name': 'modern-0',
                    'visibility': CVDataFactory.visibility_settings()
                }
                
                # Step 1: PDF generation fails
                with mock_weasyprint() as mocks:
                    mocks['HTML'].side_effect = Exception('WeasyPrint service unavailable')
                    
                    response = self.client.post(url, data=request_data, format='json')
                    self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
                
                # Step 2: PDF generation recovers
                with mock_weasyprint():
                    response = self.client.post(url, data=request_data, format='json')
                    
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                    self.assertEqual(response['Content-Type'], 'application/pdf')
                    self.assertGreater(len(response.content), 0)
    
    def test_cache_failure_recovery_workflow(self):
        """Test cache failure recovery workflow"""
        with mock_supabase_service() as (service, mock_client):
            setup_mock_supabase_user(mock_client, self.token, self.user_data)
            
            with patch('apps.cv_builder.views.SupabaseAuthentication') as mock_auth:
                mock_user = User.objects.create_user(
                    username=self.user_data['id'],
                    email=self.user_data['email']
                )
                mock_auth.return_value.authenticate.return_value = (mock_user, self.token)
                
                self._authenticate()
                
                # Step 1: Cache fails
                with patch('django.core.cache.cache.get') as mock_cache_get:
                    mock_cache_get.side_effect = Exception('Cache service unavailable')
                    
                    from django.urls import reverse
                    url = reverse('cv-data')
                    
                    # Should still work without cache
                    response = self.client.get(url)
                    self.assertEqual(response.status_code, status.HTTP_200_OK)
                
                # Step 2: Cache recovers
                cv_data = CVDataFactory.complete_cv_data()
                response = self.client.put(url, data=cv_data, format='json')
                
                self.assertEqual(response.status_code, status.HTTP_200_OK)
