"""
Comprehensive Unit Tests for Django Models

Tests all model functionality including:
- Model creation, validation, and constraints
- Relationships and cascading operations
- Custom methods and properties
- Database field behavior
- Model managers and querysets
"""

import pytest
import uuid
from datetime import datetime, timedelta
from django.test import TestCase, TransactionTestCase
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.contrib.auth.models import User

from apps.cv_builder.models import CVProfile, PDFGenerationLog, UserProfile, RateLimitLog
from .factories import (
    CVProfileFactory, PDFGenerationLogFactory, UserProfileFactory,
    RateLimitLogFactory, CVDataFactory, UserFactory
)


class CVProfileModelTest(TestCase):
    """Test CVProfile model functionality"""
    
    def setUp(self):
        self.user_id = str(uuid.uuid4())
        self.cv_data = CVDataFactory.complete_cv_data()
    
    def test_cv_profile_creation(self):
        """Test basic CV profile creation"""
        cv_profile = CVProfile.objects.create(
            user_id=self.user_id,
            cv_content=self.cv_data,
            template_name='modern-0'
        )
        
        self.assertIsInstance(cv_profile.id, uuid.UUID)
        self.assertEqual(str(cv_profile.user_id), self.user_id)
        self.assertEqual(cv_profile.template_name, 'modern-0')
        self.assertEqual(cv_profile.cv_content, self.cv_data)
        self.assertIsNotNone(cv_profile.created_at)
        self.assertIsNotNone(cv_profile.updated_at)
    
    def test_cv_profile_default_values(self):
        """Test default values for CV profile"""
        cv_profile = CVProfile.objects.create(user_id=self.user_id)
        
        self.assertEqual(cv_profile.template_name, 'classic-0')
        self.assertEqual(cv_profile.cv_content, {})
    
    def test_cv_profile_template_choices(self):
        """Test template name validation with choices"""
        valid_templates = ['classic-0', 'modern-0', 'modern-1', 'academic-0']
        
        for template in valid_templates:
            cv_profile = CVProfile.objects.create(
                user_id=self.user_id,
                template_name=template
            )
            self.assertEqual(cv_profile.template_name, template)
    
    def test_cv_profile_json_field_operations(self):
        """Test JSON field operations and queries"""
        cv_profile = CVProfileFactory(user_id=self.user_id)
        
        # Test JSON field access
        personal_info = cv_profile.cv_content.get('personalInfo', {})
        self.assertIsInstance(personal_info, dict)
        
        # Test JSON field update
        cv_profile.cv_content['personalInfo']['firstName'] = 'UpdatedName'
        cv_profile.save()
        
        cv_profile.refresh_from_db()
        self.assertEqual(
            cv_profile.cv_content['personalInfo']['firstName'], 
            'UpdatedName'
        )
    
    def test_cv_profile_str_representation(self):
        """Test string representation"""
        cv_profile = CVProfileFactory()
        expected = f"CV Profile {cv_profile.id}"
        self.assertEqual(str(cv_profile), expected)
    
    def test_cv_profile_ordering(self):
        """Test default ordering by updated_at descending"""
        # Create profiles with different update times
        old_profile = CVProfileFactory(
            created_at=timezone.now() - timedelta(days=2)
        )
        new_profile = CVProfileFactory(
            created_at=timezone.now() - timedelta(days=1)
        )
        
        profiles = list(CVProfile.objects.all())
        self.assertEqual(profiles[0], new_profile)
        self.assertEqual(profiles[1], old_profile)
    
    def test_cv_profile_database_table(self):
        """Test correct database table name"""
        self.assertEqual(CVProfile._meta.db_table, 'cv_data')
    
    def test_cv_profile_complex_cv_data(self):
        """Test complex CV data structures"""
        complex_data = {
            'personalInfo': CVDataFactory.personal_info(),
            'workExperience': CVDataFactory.work_experience(5),
            'education': CVDataFactory.education(3),
            'skills': CVDataFactory.skills(),
            'projects': CVDataFactory.projects(4),
            'references': CVDataFactory.references(3),
            'interests': CVDataFactory.interests(),
            'visibility': CVDataFactory.visibility_settings(),
            'customSections': {
                'awards': [
                    {'name': 'Test Award', 'year': 2023},
                    {'name': 'Another Award', 'year': 2022}
                ],
                'certifications': [
                    {'name': 'AWS Certified', 'date': '2023-01-15'}
                ]
            }
        }
        
        cv_profile = CVProfile.objects.create(
            user_id=self.user_id,
            cv_content=complex_data
        )
        
        cv_profile.refresh_from_db()
        self.assertEqual(len(cv_profile.cv_content['workExperience']), 5)
        self.assertEqual(len(cv_profile.cv_content['education']), 3)
        self.assertIn('customSections', cv_profile.cv_content)


class PDFGenerationLogModelTest(TestCase):
    """Test PDFGenerationLog model functionality"""
    
    def setUp(self):
        self.user_id = str(uuid.uuid4())
        self.cv_profile = CVProfileFactory(user_id=self.user_id)
    
    def test_pdf_generation_log_creation(self):
        """Test basic PDF generation log creation"""
        pdf_log = PDFGenerationLog.objects.create(
            user_id=self.user_id,
            cv_profile=self.cv_profile,
            template_name='modern-0',
            status='pending'
        )
        
        self.assertIsInstance(pdf_log.id, uuid.UUID)
        self.assertEqual(str(pdf_log.user_id), self.user_id)
        self.assertEqual(pdf_log.cv_profile, self.cv_profile)
        self.assertEqual(pdf_log.template_name, 'modern-0')
        self.assertEqual(pdf_log.status, 'pending')
        self.assertEqual(pdf_log.priority, 'normal')  # default
        self.assertEqual(pdf_log.retry_count, 0)  # default
        self.assertEqual(pdf_log.max_retries, 3)  # default
    
    def test_pdf_generation_log_status_choices(self):
        """Test all status choices"""
        statuses = ['pending', 'processing', 'completed', 'failed', 'cancelled']
        
        for status in statuses:
            pdf_log = PDFGenerationLog.objects.create(
                user_id=self.user_id,
                cv_profile=self.cv_profile,
                template_name='classic-0',
                status=status
            )
            self.assertEqual(pdf_log.status, status)
    
    def test_pdf_generation_log_priority_choices(self):
        """Test all priority choices"""
        priorities = ['low', 'normal', 'high', 'urgent']
        
        for priority in priorities:
            pdf_log = PDFGenerationLog.objects.create(
                user_id=self.user_id,
                cv_profile=self.cv_profile,
                template_name='classic-0',
                priority=priority
            )
            self.assertEqual(pdf_log.priority, priority)
    
    def test_pdf_generation_log_properties(self):
        """Test custom properties"""
        # Test is_processing
        pending_log = PDFGenerationLogFactory(status='pending')
        processing_log = PDFGenerationLogFactory(status='processing')
        completed_log = PDFGenerationLogFactory(status='completed')
        failed_log = PDFGenerationLogFactory(status='failed')
        
        self.assertTrue(pending_log.is_processing)
        self.assertTrue(processing_log.is_processing)
        self.assertFalse(completed_log.is_processing)
        self.assertFalse(failed_log.is_processing)
        
        # Test is_completed
        self.assertFalse(pending_log.is_completed)
        self.assertTrue(completed_log.is_completed)
        
        # Test is_failed
        self.assertFalse(pending_log.is_failed)
        self.assertTrue(failed_log.is_failed)
        
        # Test can_retry
        self.assertFalse(pending_log.can_retry)
        self.assertTrue(failed_log.can_retry)
        
        # Test retry limit
        failed_log.retry_count = 3
        self.assertFalse(failed_log.can_retry)
    
    def test_pdf_generation_log_processing_duration(self):
        """Test processing duration calculation"""
        start_time = timezone.now()
        end_time = start_time + timedelta(seconds=45)
        
        pdf_log = PDFGenerationLog.objects.create(
            user_id=self.user_id,
            cv_profile=self.cv_profile,
            template_name='modern-0',
            status='completed',
            processing_started_at=start_time,
            processing_completed_at=end_time
        )
        
        expected_duration = end_time - start_time
        self.assertEqual(pdf_log.processing_duration, expected_duration)
    
    def test_pdf_generation_log_metadata(self):
        """Test metadata JSON field"""
        metadata = {
            'user_agent': 'Mozilla/5.0 Test Browser',
            'ip_address': '*************',
            'source': 'web',
            'version': '1.0.0',
            'custom_options': {
                'page_size': 'A4',
                'orientation': 'portrait'
            }
        }
        
        pdf_log = PDFGenerationLog.objects.create(
            user_id=self.user_id,
            cv_profile=self.cv_profile,
            template_name='modern-0',
            metadata=metadata
        )
        
        pdf_log.refresh_from_db()
        self.assertEqual(pdf_log.metadata, metadata)
        self.assertEqual(pdf_log.metadata['source'], 'web')
        self.assertEqual(pdf_log.metadata['custom_options']['page_size'], 'A4')
    
    def test_pdf_generation_log_cascade_delete(self):
        """Test cascade delete when CV profile is deleted"""
        pdf_log = PDFGenerationLogFactory(cv_profile=self.cv_profile)
        pdf_log_id = pdf_log.id
        
        self.cv_profile.delete()
        
        with self.assertRaises(PDFGenerationLog.DoesNotExist):
            PDFGenerationLog.objects.get(id=pdf_log_id)
    
    def test_pdf_generation_log_str_representation(self):
        """Test string representation"""
        pdf_log = PDFGenerationLogFactory(status='processing')
        expected = f"PDF generation {pdf_log.id} (processing)"
        self.assertEqual(str(pdf_log), expected)
    
    def test_pdf_generation_log_indexes(self):
        """Test database indexes exist"""
        indexes = [index.name for index in PDFGenerationLog._meta.indexes]
        
        # Check that expected indexes are present
        self.assertTrue(any('user_id' in str(index) for index in PDFGenerationLog._meta.indexes))
        self.assertTrue(any('status' in str(index) for index in PDFGenerationLog._meta.indexes))
        self.assertTrue(any('created_at' in str(index) for index in PDFGenerationLog._meta.indexes))
        self.assertTrue(any('task_id' in str(index) for index in PDFGenerationLog._meta.indexes))


class UserProfileModelTest(TestCase):
    """Test UserProfile model functionality"""
    
    def setUp(self):
        self.user_id = uuid.uuid4()
        self.email = '<EMAIL>'
    
    def test_user_profile_creation(self):
        """Test basic user profile creation"""
        profile = UserProfile.objects.create(
            id=self.user_id,
            email=self.email,
            full_name='Test User'
        )
        
        self.assertEqual(profile.id, self.user_id)
        self.assertEqual(profile.email, self.email)
        self.assertEqual(profile.full_name, 'Test User')
        self.assertEqual(profile.preferences, {})
        self.assertIsNotNone(profile.created_at)
        self.assertIsNotNone(profile.updated_at)
    
    def test_user_profile_preferences(self):
        """Test preferences JSON field"""
        preferences = {
            'theme': 'dark',
            'notifications': True,
            'language': 'en',
            'timezone': 'UTC',
            'pdf_settings': {
                'default_template': 'modern-0',
                'auto_download': False
            }
        }
        
        profile = UserProfile.objects.create(
            id=self.user_id,
            email=self.email,
            preferences=preferences
        )
        
        profile.refresh_from_db()
        self.assertEqual(profile.preferences, preferences)
        self.assertEqual(profile.preferences['theme'], 'dark')
        self.assertEqual(profile.preferences['pdf_settings']['default_template'], 'modern-0')
    
    def test_user_profile_str_representation(self):
        """Test string representation"""
        # With email
        profile_with_email = UserProfile.objects.create(
            id=self.user_id,
            email=self.email
        )
        self.assertEqual(str(profile_with_email), f"User Profile {self.email}")
        
        # Without email
        user_id_2 = uuid.uuid4()
        profile_without_email = UserProfile.objects.create(id=user_id_2)
        self.assertEqual(str(profile_without_email), f"User Profile {user_id_2}")
    
    def test_user_profile_database_table(self):
        """Test correct database table name"""
        self.assertEqual(UserProfile._meta.db_table, 'user_profiles')
    
    def test_user_profile_avatar_url_validation(self):
        """Test avatar URL field validation"""
        profile = UserProfile.objects.create(
            id=self.user_id,
            email=self.email,
            avatar_url='https://example.com/avatar.jpg'
        )
        
        self.assertEqual(profile.avatar_url, 'https://example.com/avatar.jpg')


class RateLimitLogModelTest(TestCase):
    """Test RateLimitLog model functionality"""
    
    def setUp(self):
        self.user_id = str(uuid.uuid4())
        self.ip_address = '*************'
    
    def test_rate_limit_log_creation(self):
        """Test basic rate limit log creation"""
        rate_log = RateLimitLog.objects.create(
            user_id=self.user_id,
            action='pdf_generation',
            ip_address=self.ip_address,
            endpoint='/api/pdf/generate/'
        )
        
        self.assertIsInstance(rate_log.id, uuid.UUID)
        self.assertEqual(str(rate_log.user_id), self.user_id)
        self.assertEqual(rate_log.action, 'pdf_generation')
        self.assertEqual(rate_log.ip_address, self.ip_address)
        self.assertEqual(rate_log.endpoint, '/api/pdf/generate/')
        self.assertIsNotNone(rate_log.created_at)
    
    def test_rate_limit_log_request_data(self):
        """Test request_data JSON field"""
        request_data = {
            'template_name': 'modern-0',
            'timestamp': timezone.now().isoformat(),
            'request_size': 25000,
            'headers': {
                'User-Agent': 'Mozilla/5.0',
                'Accept': 'application/pdf'
            }
        }
        
        rate_log = RateLimitLog.objects.create(
            user_id=self.user_id,
            action='pdf_generation',
            request_data=request_data
        )
        
        rate_log.refresh_from_db()
        self.assertEqual(rate_log.request_data, request_data)
        self.assertEqual(rate_log.request_data['template_name'], 'modern-0')
        self.assertEqual(rate_log.request_data['request_size'], 25000)
    
    def test_rate_limit_log_str_representation(self):
        """Test string representation"""
        rate_log = RateLimitLogFactory(
            user_id=self.user_id,
            action='pdf_generation'
        )
        expected = f"Rate limit log {self.user_id} - pdf_generation"
        self.assertEqual(str(rate_log), expected)
    
    def test_rate_limit_log_database_table(self):
        """Test correct database table name"""
        self.assertEqual(RateLimitLog._meta.db_table, 'rate_limit_logs')
    
    def test_rate_limit_log_indexes(self):
        """Test database indexes exist"""
        # Check that expected indexes are present
        self.assertTrue(any('user_id' in str(index) for index in RateLimitLog._meta.indexes))
        self.assertTrue(any('ip_address' in str(index) for index in RateLimitLog._meta.indexes))


class ModelRelationshipTest(TestCase):
    """Test relationships between models"""
    
    def setUp(self):
        self.user_id = str(uuid.uuid4())
        self.cv_profile = CVProfileFactory(user_id=self.user_id)
    
    def test_cv_profile_to_pdf_generation_relationship(self):
        """Test one-to-many relationship between CV profile and PDF generations"""
        # Create multiple PDF generations for the same CV profile
        pdf_gen1 = PDFGenerationLogFactory(
            cv_profile=self.cv_profile,
            status='completed'
        )
        pdf_gen2 = PDFGenerationLogFactory(
            cv_profile=self.cv_profile,
            status='failed'
        )
        
        # Test reverse relationship
        pdf_generations = self.cv_profile.pdf_generations.all()
        self.assertEqual(pdf_generations.count(), 2)
        self.assertIn(pdf_gen1, pdf_generations)
        self.assertIn(pdf_gen2, pdf_generations)
        
        # Test forward relationship
        self.assertEqual(pdf_gen1.cv_profile, self.cv_profile)
        self.assertEqual(pdf_gen2.cv_profile, self.cv_profile)
    
    def test_cascade_delete_protection(self):
        """Test that deleting CV profile cascades to PDF generations"""
        pdf_gen = PDFGenerationLogFactory(cv_profile=self.cv_profile)
        pdf_gen_id = pdf_gen.id
        
        # Delete CV profile should cascade to PDF generations
        self.cv_profile.delete()
        
        with self.assertRaises(PDFGenerationLog.DoesNotExist):
            PDFGenerationLog.objects.get(id=pdf_gen_id)


class ModelValidationTest(TestCase):
    """Test model validation and constraints"""
    
    def test_cv_profile_user_id_required(self):
        """Test that user_id is required for CV profile"""
        with self.assertRaises(IntegrityError):
            CVProfile.objects.create(cv_content={})
    
    def test_pdf_generation_log_required_fields(self):
        """Test required fields for PDF generation log"""
        cv_profile = CVProfileFactory()
        
        # Should work with minimum required fields
        pdf_log = PDFGenerationLog.objects.create(
            user_id=str(uuid.uuid4()),
            cv_profile=cv_profile,
            template_name='classic-0'
        )
        self.assertIsNotNone(pdf_log.id)
    
    def test_user_profile_unique_id(self):
        """Test that user profile ID is unique"""
        user_id = uuid.uuid4()
        
        UserProfile.objects.create(
            id=user_id,
            email='<EMAIL>'
        )
        
        with self.assertRaises(IntegrityError):
            UserProfile.objects.create(
                id=user_id,
                email='<EMAIL>'
            )


class ModelQueryTest(TestCase):
    """Test model querying capabilities"""
    
    def setUp(self):
        self.user_id = str(uuid.uuid4())
        self.cv_profile = CVProfileFactory(user_id=self.user_id)
    
    def test_cv_profile_user_filtering(self):
        """Test filtering CV profiles by user"""
        other_user_id = str(uuid.uuid4())
        other_cv_profile = CVProfileFactory(user_id=other_user_id)
        
        user_profiles = CVProfile.objects.filter(user_id=self.user_id)
        self.assertEqual(user_profiles.count(), 1)
        self.assertEqual(user_profiles.first(), self.cv_profile)
        
        other_user_profiles = CVProfile.objects.filter(user_id=other_user_id)
        self.assertEqual(other_user_profiles.count(), 1)
        self.assertEqual(other_user_profiles.first(), other_cv_profile)
    
    def test_pdf_generation_status_filtering(self):
        """Test filtering PDF generations by status"""
        completed_gen = PDFGenerationLogFactory(
            cv_profile=self.cv_profile,
            status='completed'
        )
        failed_gen = PDFGenerationLogFactory(
            cv_profile=self.cv_profile,
            status='failed'
        )
        pending_gen = PDFGenerationLogFactory(
            cv_profile=self.cv_profile,
            status='pending'
        )
        
        # Test single status filter
        completed_gens = PDFGenerationLog.objects.filter(status='completed')
        self.assertEqual(completed_gens.count(), 1)
        self.assertEqual(completed_gens.first(), completed_gen)
        
        # Test multiple status filter
        active_gens = PDFGenerationLog.objects.filter(
            status__in=['pending', 'processing']
        )
        self.assertEqual(active_gens.count(), 1)
        self.assertEqual(active_gens.first(), pending_gen)
    
    def test_rate_limit_time_based_queries(self):
        """Test time-based queries for rate limiting"""
        now = timezone.now()
        hour_ago = now - timedelta(hours=1)
        two_hours_ago = now - timedelta(hours=2)
        
        # Create rate limit logs at different times
        with patch('django.utils.timezone.now', return_value=two_hours_ago):
            old_log = RateLimitLogFactory(user_id=self.user_id)
        
        with patch('django.utils.timezone.now', return_value=hour_ago):
            recent_log = RateLimitLogFactory(user_id=self.user_id)
        
        # Query logs from last hour
        recent_logs = RateLimitLog.objects.filter(
            user_id=self.user_id,
            created_at__gte=now - timedelta(hours=1)
        )
        
        self.assertEqual(recent_logs.count(), 1)
        self.assertEqual(recent_logs.first(), recent_log)
    
    def test_json_field_queries(self):
        """Test querying JSON fields"""
        cv_data_with_skills = CVDataFactory.complete_cv_data()
        cv_data_with_skills['skills']['technical'] = ['Python', 'Django', 'React']
        
        cv_profile = CVProfile.objects.create(
            user_id=self.user_id,
            cv_content=cv_data_with_skills
        )
        
        # Note: JSON field queries depend on database backend
        # This is a basic test that should work with most backends
        profiles_with_skills = CVProfile.objects.filter(
            cv_content__has_key='skills'
        )
        
        self.assertIn(cv_profile, profiles_with_skills)


class ModelPerformanceTest(TestCase):
    """Test model performance and optimization"""
    
    def test_bulk_cv_profile_creation(self):
        """Test bulk creation of CV profiles"""
        profiles_data = []
        for i in range(100):
            profiles_data.append(CVProfile(
                user_id=str(uuid.uuid4()),
                cv_content=CVDataFactory.complete_cv_data(),
                template_name='modern-0'
            ))
        
        # Bulk create should be efficient
        created_profiles = CVProfile.objects.bulk_create(profiles_data)
        self.assertEqual(len(created_profiles), 100)
    
    def test_pdf_generation_log_bulk_operations(self):
        """Test bulk operations on PDF generation logs"""
        cv_profiles = [CVProfileFactory() for _ in range(10)]
        
        pdf_logs_data = []
        for cv_profile in cv_profiles:
            pdf_logs_data.append(PDFGenerationLog(
                user_id=str(uuid.uuid4()),
                cv_profile=cv_profile,
                template_name='classic-0',
                status='completed'
            ))
        
        created_logs = PDFGenerationLog.objects.bulk_create(pdf_logs_data)
        self.assertEqual(len(created_logs), 10)
        
        # Bulk update
        PDFGenerationLog.objects.filter(
            id__in=[log.id for log in created_logs]
        ).update(status='archived')
        
        updated_count = PDFGenerationLog.objects.filter(
            status='archived'
        ).count()
        self.assertEqual(updated_count, 10)
