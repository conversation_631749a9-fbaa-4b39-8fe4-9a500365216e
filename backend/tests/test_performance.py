"""
Comprehensive Performance and Load Testing Suite

Tests performance characteristics of the Django + Supabase + WeasyPrint integration:
- Database query performance and optimization
- PDF generation performance under load
- API response times and throughput
- Memory usage and resource management
- Concurrent request handling
- Rate limiting and caching effectiveness
- Load testing scenarios and stress testing
- Benchmark tests for performance regression detection
"""

import pytest
import time
import threading
import psutil
import gc
import uuid
import multiprocessing
import statistics
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
from unittest.mock import patch, Mock
from django.test import TestCase, TransactionTestCase, override_settings
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction, connections
from django.test.utils import override_settings
from django.contrib.auth.models import User
from rest_framework.test import APITestCase, APIClient

from apps.cv_builder.models import CVProfile, PDFGenerationLog, UserProfile, RateLimitLog
from apps.authentication.backends import SupabaseService
from .factories import (
    CVProfileFactory, PDFGenerationLogFactory, UserProfileFactory,
    CVDataFactory, SupabaseUserDataFactory, MockDataGenerator
)
from .mocks import (
    mock_weasyprint, mock_supabase_service, mock_celery_tasks,
    apply_test_settings
)


class PerformanceTestMixin:
    """Mixin for performance testing utilities"""

    def measure_time(self, func, *args, **kwargs):
        """Measure execution time of a function"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        return result, end_time - start_time

    def measure_memory(self, func, *args, **kwargs):
        """Measure memory usage of a function"""
        try:
            import psutil
            process = psutil.Process()

            # Force garbage collection before measurement
            gc.collect()
            memory_before = process.memory_info().rss

            result = func(*args, **kwargs)

            gc.collect()
            memory_after = process.memory_info().rss

            memory_delta = memory_after - memory_before
            return result, memory_delta
        except ImportError:
            # psutil not available, just return result
            return func(*args, **kwargs), 0

    def run_concurrent_operations(self, operation, count, max_workers=None):
        """Run operations concurrently and measure performance"""
        if max_workers is None:
            max_workers = min(count, multiprocessing.cpu_count())

        results = []
        errors = []
        times = []

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            start_time = time.time()

            futures = [executor.submit(operation) for _ in range(count)]

            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    errors.append(e)

            total_time = time.time() - start_time

        return {
            'results': results,
            'errors': errors,
            'total_time': total_time,
            'success_rate': len(results) / count if count > 0 else 0,
            'throughput': len(results) / total_time if total_time > 0 else 0
        }


@pytest.mark.performance
@apply_test_settings()
class DatabasePerformanceTest(TestCase, PerformanceTestMixin):
    """Test database performance and optimization"""
    
    def setUp(self):
        cache.clear()
        self.user_id = str(SupabaseUserDataFactory.create_user_data()['id'])
    
    def tearDown(self):
        cache.clear()
    
    def test_cv_profile_query_performance(self):
        """Test CV profile query performance with large datasets"""
        # Create many CV profiles
        profiles = []
        for i in range(1000):
            profile = CVProfile(
                user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                cv_content=CVDataFactory.complete_cv_data(),
                template_name=f'template-{i % 4}'
            )
            profiles.append(profile)
        
        # Bulk create for efficiency
        start_time = time.time()
        CVProfile.objects.bulk_create(profiles, batch_size=100)
        creation_time = time.time() - start_time
        
        # Should create 1000 profiles efficiently
        self.assertLess(creation_time, 10.0)  # Under 10 seconds
        
        # Test query performance
        start_time = time.time()
        recent_profiles = CVProfile.objects.filter(
            created_at__gte=timezone.now() - timezone.timedelta(hours=1)
        )[:100]
        list(recent_profiles)  # Force evaluation
        query_time = time.time() - start_time
        
        # Query should be fast even with large dataset
        self.assertLess(query_time, 1.0)  # Under 1 second
    
    def test_json_field_query_performance(self):
        """Test JSON field query performance"""
        # Create profiles with varying JSON content
        profiles = []
        for i in range(500):
            cv_data = CVDataFactory.complete_cv_data()
            if i % 2 == 0:
                cv_data['skills']['technical'] = ['Python', 'Django', 'React']
            else:
                cv_data['skills']['technical'] = ['JavaScript', 'Node.js', 'Vue']
            
            profile = CVProfile(
                user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                cv_content=cv_data
            )
            profiles.append(profile)
        
        CVProfile.objects.bulk_create(profiles, batch_size=100)
        
        # Test JSON field queries
        start_time = time.time()
        profiles_with_skills = CVProfile.objects.filter(
            cv_content__has_key='skills'
        )[:50]
        list(profiles_with_skills)
        json_query_time = time.time() - start_time
        
        # JSON queries should be reasonably fast
        self.assertLess(json_query_time, 2.0)  # Under 2 seconds
    
    def test_relationship_query_performance(self):
        """Test relationship query performance"""
        # Create CV profiles with PDF generations
        cv_profiles = []
        for i in range(100):
            profile = CVProfileFactory()
            cv_profiles.append(profile)
        
        # Create multiple PDF generations for each profile
        pdf_generations = []
        for profile in cv_profiles:
            for j in range(5):  # 5 PDFs per profile
                pdf_gen = PDFGenerationLog(
                    user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                    cv_profile=profile,
                    template_name='modern-0',
                    status='completed'
                )
                pdf_generations.append(pdf_gen)
        
        PDFGenerationLog.objects.bulk_create(pdf_generations, batch_size=100)
        
        # Test relationship queries
        start_time = time.time()
        
        # Query profiles with their PDF generations
        profiles_with_pdfs = CVProfile.objects.prefetch_related(
            'pdf_generations'
        )[:50]
        
        for profile in profiles_with_pdfs:
            list(profile.pdf_generations.all())  # Force evaluation
        
        relationship_query_time = time.time() - start_time
        
        # Relationship queries should be optimized
        self.assertLess(relationship_query_time, 3.0)  # Under 3 seconds
    
    @pytest.mark.slow
    def test_bulk_operations_performance(self):
        """Test bulk operations performance"""
        # Test bulk update performance
        profiles = [CVProfileFactory() for _ in range(1000)]
        
        start_time = time.time()
        CVProfile.objects.filter(
            id__in=[p.id for p in profiles[:500]]
        ).update(template_name='updated-template')
        bulk_update_time = time.time() - start_time
        
        # Bulk update should be efficient
        self.assertLess(bulk_update_time, 5.0)  # Under 5 seconds
        
        # Test bulk delete performance
        start_time = time.time()
        CVProfile.objects.filter(
            id__in=[p.id for p in profiles[500:]]
        ).delete()
        bulk_delete_time = time.time() - start_time
        
        # Bulk delete should be efficient
        self.assertLess(bulk_delete_time, 5.0)  # Under 5 seconds
    
    def test_database_connection_performance(self):
        """Test database connection and pool performance"""
        def perform_database_operation():
            """Perform a simple database operation"""
            return CVProfile.objects.count()
        
        # Test concurrent database operations
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(perform_database_operation) for _ in range(50)]
            results = [future.result() for future in as_completed(futures)]
        
        concurrent_time = time.time() - start_time
        
        # Concurrent operations should complete efficiently
        self.assertLess(concurrent_time, 10.0)  # Under 10 seconds
        self.assertEqual(len(results), 50)  # All operations should succeed


@pytest.mark.performance
@apply_test_settings()
class PDFGenerationPerformanceTest(TestCase, PerformanceTestMixin):
    """Test PDF generation performance"""
    
    def setUp(self):
        cache.clear()
        self.cv_data = CVDataFactory.complete_cv_data()
        self.visibility = CVDataFactory.visibility_settings()
    
    def tearDown(self):
        cache.clear()
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_single_pdf_generation_performance(self):
        """Test single PDF generation performance"""
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            service = WeasyPrintService()

            def generate_pdf():
                return service.generate_pdf(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )

            pdf_bytes, generation_time = self.measure_time(generate_pdf)

            self.assertIsInstance(pdf_bytes, bytes)
            self.assertGreater(len(pdf_bytes), 0)
            self.assertLess(generation_time, 5.0)  # Should generate within 5 seconds
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_concurrent_pdf_generation_performance(self):
        """Test concurrent PDF generation performance"""
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            service = WeasyPrintService()

            def generate_pdf():
                return service.generate_pdf(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )

            # Test with multiple concurrent generations
            performance = self.run_concurrent_operations(
                generate_pdf,
                count=10,
                max_workers=3
            )

            self.assertGreater(performance['success_rate'], 0.8)  # 80% success rate
            self.assertLess(performance['total_time'], 30.0)  # Complete within 30 seconds
            self.assertGreater(performance['throughput'], 0.2)  # At least 0.2 PDFs/second

            # All results should be valid PDFs
            for pdf_bytes in performance['results']:
                self.assertIsInstance(pdf_bytes, bytes)
                self.assertGreater(len(pdf_bytes), 0)

    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_template_performance_comparison(self):
        """Test performance comparison between different templates"""
        templates = ['classic-0', 'modern-0', 'modern-1', 'academic-0']
        template_performance = {}

        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            service = WeasyPrintService()

            for template in templates:
                def generate_pdf_with_template():
                    return service.generate_pdf(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name=template
                    )

                pdf_bytes, generation_time = self.measure_time(generate_pdf_with_template)
                template_performance[template] = generation_time

            # All templates should perform reasonably
            for template, time_taken in template_performance.items():
                self.assertLess(time_taken, 8.0, f"Template {template} took too long: {time_taken}s")

            # Performance should be relatively consistent
            times = list(template_performance.values())
            max_time = max(times)
            min_time = min(times)
            # Max time should not be more than 3x min time
            self.assertLess(max_time, min_time * 3)

    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_large_cv_pdf_generation_performance(self):
        """Test PDF generation performance with large CV data"""
        # Create large CV data
        large_cv_data = CVDataFactory.complete_cv_data()
        large_cv_data['workExperience'] = CVDataFactory.work_experience(50)  # 50 jobs
        large_cv_data['projects'] = CVDataFactory.projects(100)  # 100 projects
        large_cv_data['education'] = CVDataFactory.education(20)  # 20 degrees
        
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            
            service = WeasyPrintService()
            
            start_time = time.time()
            pdf_bytes = service.generate_pdf(
                cv_data=large_cv_data,
                visibility=self.visibility,
                template_name='academic-0'
            )
            generation_time = time.time() - start_time
            
            # Large CV should still generate in reasonable time
            self.assertLess(generation_time, 10.0)  # Under 10 seconds
            self.assertIsInstance(pdf_bytes, bytes)
            self.assertGreater(len(pdf_bytes), 0)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_memory_usage(self):
        """Test PDF generation memory usage"""
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            
            # Get initial memory usage
            process = psutil.Process()
            initial_memory = process.memory_info().rss
            
            service = WeasyPrintService()
            
            # Generate multiple PDFs and monitor memory
            for i in range(10):
                pdf_bytes = service.generate_pdf(
                    cv_data=self.cv_data,
                    visibility=self.visibility,
                    template_name='modern-0'
                )
                
                current_memory = process.memory_info().rss
                memory_increase = (current_memory - initial_memory) / 1024 / 1024  # MB
                
                # Memory usage should not grow excessively
                self.assertLess(memory_increase, 500)  # Under 500MB increase
                
                # Force garbage collection
                gc.collect()
    
    @pytest.mark.slow
    def test_pdf_generation_stress_test(self):
        """Test PDF generation under stress"""
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            
            def stress_generate_pdf(iteration):
                try:
                    service = WeasyPrintService()
                    pdf_bytes = service.generate_pdf(
                        cv_data=self.cv_data,
                        visibility=self.visibility,
                        template_name=f'template-{iteration % 3}'
                    )
                    return len(pdf_bytes)
                except Exception as e:
                    return str(e)
            
            start_time = time.time()
            
            # Generate 50 PDFs with high concurrency
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [
                    executor.submit(stress_generate_pdf, i) 
                    for i in range(50)
                ]
                results = [future.result() for future in as_completed(futures)]
            
            stress_time = time.time() - start_time
            
            # Stress test should complete without errors
            self.assertLess(stress_time, 60.0)  # Under 1 minute
            
            # Most results should be successful (PDF sizes)
            successful_results = [r for r in results if isinstance(r, int)]
            self.assertGreater(len(successful_results), 40)  # At least 80% success


@pytest.mark.performance
@apply_test_settings()
class APIPerformanceTest(TestCase, PerformanceTestMixin):
    """Test API performance characteristics"""
    
    def setUp(self):
        cache.clear()
        self.user_data = SupabaseUserDataFactory.create_user_data()
    
    def tearDown(self):
        cache.clear()
    
    def test_api_response_time_benchmarks(self):
        """Test API response time benchmarks"""
        # Create test data
        cv_profile = CVProfileFactory(
            user_id=self.user_data['id'],
            cv_content=CVDataFactory.complete_cv_data()
        )
        
        with mock_supabase_service() as (service, mock_client):
            from .mocks import setup_mock_supabase_user
            setup_mock_supabase_user(mock_client, 'perf-token', self.user_data)
            
            # Simulate API request processing
            start_time = time.time()
            
            # Mock API endpoint operations
            for _ in range(100):  # 100 requests
                # Simulate authentication
                user_data = service.verify_token('perf-token')
                
                # Simulate database query
                profiles = CVProfile.objects.filter(user_id=user_data['id'])[:10]
                list(profiles)
            
            total_time = time.time() - start_time
            avg_response_time = total_time / 100
            
            # Average response time should be reasonable
            self.assertLess(avg_response_time, 0.1)  # Under 100ms per request
    
    def test_concurrent_api_requests_performance(self):
        """Test concurrent API requests performance"""
        # Create test data
        for i in range(100):
            CVProfileFactory(user_id=str(SupabaseUserDataFactory.create_user_data()['id']))
        
        def simulate_api_request():
            """Simulate an API request"""
            try:
                # Simulate database operations
                profiles = CVProfile.objects.all()[:10]
                return len(list(profiles))
            except Exception as e:
                return str(e)
        
        start_time = time.time()
        
        # Simulate 50 concurrent API requests
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(simulate_api_request) for _ in range(50)]
            results = [future.result() for future in as_completed(futures)]
        
        concurrent_time = time.time() - start_time
        
        # Concurrent requests should complete efficiently
        self.assertLess(concurrent_time, 10.0)  # Under 10 seconds
        
        # All requests should succeed
        successful_results = [r for r in results if isinstance(r, int)]
        self.assertEqual(len(successful_results), 50)
    
    def test_api_caching_performance(self):
        """Test API caching performance improvements"""
        with mock_supabase_service() as (service, mock_client):
            from .mocks import setup_mock_supabase_user
            setup_mock_supabase_user(mock_client, 'cache-token', self.user_data)
            
            # First request (cache miss)
            start_time = time.time()
            result1 = service.verify_token('cache-token')
            first_request_time = time.time() - start_time
            
            # Second request (cache hit)
            start_time = time.time()
            result2 = service.verify_token('cache-token')
            second_request_time = time.time() - start_time
            
            # Results should be identical
            self.assertEqual(result1, result2)
            
            # Cached request should be significantly faster
            self.assertLess(second_request_time, first_request_time * 0.5)
    
    @pytest.mark.slow
    def test_api_load_testing(self):
        """Test API under load"""
        # Create substantial test data
        profiles = [CVProfileFactory() for _ in range(500)]
        
        def api_load_test():
            """Perform various API operations"""
            operations = []
            
            # Query operations
            start = time.time()
            CVProfile.objects.all()[:20]
            operations.append(('query', time.time() - start))
            
            # Filter operations  
            start = time.time()
            CVProfile.objects.filter(template_name='modern-0')[:10]
            operations.append(('filter', time.time() - start))
            
            # Count operations
            start = time.time()
            CVProfile.objects.count()
            operations.append(('count', time.time() - start))
            
            return operations
        
        start_time = time.time()
        
        # Run load test with multiple threads
        with ThreadPoolExecutor(max_workers=15) as executor:
            futures = [executor.submit(api_load_test) for _ in range(100)]
            all_operations = []
            
            for future in as_completed(futures):
                operations = future.result()
                all_operations.extend(operations)
        
        total_time = time.time() - start_time
        
        # Load test should complete in reasonable time
        self.assertLess(total_time, 30.0)  # Under 30 seconds
        
        # Analyze operation performance
        query_times = [op[1] for op in all_operations if op[0] == 'query']
        filter_times = [op[1] for op in all_operations if op[0] == 'filter']
        count_times = [op[1] for op in all_operations if op[0] == 'count']
        
        # Average operation times should be reasonable
        if query_times:
            avg_query_time = sum(query_times) / len(query_times)
            self.assertLess(avg_query_time, 0.5)  # Under 500ms
        
        if filter_times:
            avg_filter_time = sum(filter_times) / len(filter_times)
            self.assertLess(avg_filter_time, 0.3)  # Under 300ms
        
        if count_times:
            avg_count_time = sum(count_times) / len(count_times)
            self.assertLess(avg_count_time, 0.1)  # Under 100ms


@apply_test_settings()
class AuthenticationPerformanceTest(TestCase, PerformanceTestMixin):
    """Test authentication performance"""

    def setUp(self):
        cache.clear()

    def tearDown(self):
        cache.clear()

    def test_jwt_verification_performance(self):
        """Test JWT verification performance"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'performance-test-token'

        with mock_supabase_service() as (service, mock_client):
            from .mocks import setup_mock_supabase_user
            setup_mock_supabase_user(mock_client, token, user_data)

            def verify_token():
                return service.verify_token(token)

            # First verification (no cache)
            result1, time1 = self.measure_time(verify_token)

            # Second verification (with cache)
            result2, time2 = self.measure_time(verify_token)

            self.assertEqual(result1, result2)
            self.assertLess(time1, 1.0)  # First verification within 1 second
            self.assertLess(time2, 0.1)  # Cached verification within 0.1 seconds
            self.assertLess(time2, time1)  # Cached should be faster

    def test_concurrent_authentication_performance(self):
        """Test concurrent authentication performance"""
        user_data = SupabaseUserDataFactory.create_user_data()
        token = 'concurrent-test-token'

        with mock_supabase_service() as (service, mock_client):
            from .mocks import setup_mock_supabase_user
            setup_mock_supabase_user(mock_client, token, user_data)

            def authenticate_user():
                result = service.verify_token(token)
                return service.get_or_create_django_user(result)

            performance = self.run_concurrent_operations(
                authenticate_user,
                count=20,
                max_workers=5
            )

            self.assertGreater(performance['success_rate'], 0.95)  # 95% success rate
            self.assertLess(performance['total_time'], 10.0)  # Complete within 10 seconds

            # All authentications should return the same user
            if performance['results']:
                first_user_id = performance['results'][0].id
                for user in performance['results']:
                    self.assertEqual(user.id, first_user_id)


@apply_test_settings()
class CachePerformanceTest(TestCase, PerformanceTestMixin):
    """Test caching performance"""

    def setUp(self):
        cache.clear()

    def tearDown(self):
        cache.clear()

    def test_cache_set_get_performance(self):
        """Test basic cache set/get performance"""
        test_data = {
            'user_id': str(uuid.uuid4()),
            'cv_data': CVDataFactory.complete_cv_data(),
            'timestamp': timezone.now().isoformat()
        }

        def cache_set_operation():
            cache.set('test_key', test_data, timeout=300)
            return True

        def cache_get_operation():
            return cache.get('test_key')

        # Test cache set performance
        result1, set_time = self.measure_time(cache_set_operation)
        self.assertTrue(result1)
        self.assertLess(set_time, 0.1)  # Cache set within 0.1 seconds

        # Test cache get performance
        result2, get_time = self.measure_time(cache_get_operation)
        self.assertEqual(result2, test_data)
        self.assertLess(get_time, 0.05)  # Cache get within 0.05 seconds

    def test_concurrent_cache_operations(self):
        """Test concurrent cache operations"""
        def cache_operation(key_suffix):
            key = f'concurrent_key_{key_suffix}'
            data = {'id': key_suffix, 'timestamp': time.time()}

            cache.set(key, data, timeout=300)
            return cache.get(key)

        # Test concurrent cache operations
        operations = [lambda i=i: cache_operation(i) for i in range(20)]

        start_time = time.time()
        results = []
        errors = []

        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(op) for op in operations]

            for i, future in enumerate(as_completed(futures)):
                try:
                    result = future.result()
                    self.assertIsNotNone(result)
                    results.append(result)
                except Exception as e:
                    errors.append(e)

        total_time = time.time() - start_time

        self.assertEqual(len(errors), 0)  # No cache errors
        self.assertEqual(len(results), 20)  # All operations successful
        self.assertLess(total_time, 5.0)  # Complete within 5 seconds


@apply_test_settings()
class MemoryUsageTest(TestCase, PerformanceTestMixin):
    """Test memory usage patterns"""

    def setUp(self):
        cache.clear()
        gc.collect()  # Clean up before tests

    def tearDown(self):
        cache.clear()
        gc.collect()  # Clean up after tests

    def test_cv_profile_memory_usage(self):
        """Test memory usage when creating CV profiles"""
        def create_many_profiles():
            profiles = []
            for i in range(100):
                profile = CVProfile(
                    user_id=str(uuid.uuid4()),
                    cv_content=CVDataFactory.complete_cv_data(),
                    template_name='modern-0'
                )
                profiles.append(profile)

            # Bulk create to test database memory usage
            return CVProfile.objects.bulk_create(profiles)

        created_profiles, memory_delta = self.measure_memory(create_many_profiles)

        self.assertEqual(len(created_profiles), 100)
        # Memory usage should be reasonable (less than 50MB for 100 profiles)
        self.assertLess(abs(memory_delta), 50 * 1024 * 1024)


@pytest.mark.performance
@pytest.mark.slow
@apply_test_settings()
class LoadTestingScenarios(TransactionTestCase, PerformanceTestMixin):
    """Comprehensive load testing scenarios"""

    def setUp(self):
        cache.clear()

    def tearDown(self):
        cache.clear()

    def test_cv_creation_load(self):
        """Test CV creation under load"""
        def create_cv():
            """Simulate CV creation"""
            return CVProfile.objects.create(
                user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                cv_content=CVDataFactory.complete_cv_data(),
                template_name='modern-0'
            )

        performance = self.run_concurrent_operations(
            create_cv,
            count=100,
            max_workers=15
        )

        # CV creation should handle load efficiently
        self.assertGreater(performance['success_rate'], 0.9)  # 90% success rate
        self.assertLess(performance['total_time'], 45.0)  # Under 45 seconds
        self.assertEqual(len(performance['results']), int(100 * performance['success_rate']))

    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_load(self):
        """Test PDF generation under load"""
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService

            def generate_pdf():
                """Simulate PDF generation"""
                service = WeasyPrintService()
                return service.generate_pdf(
                    cv_data=CVDataFactory.complete_cv_data(),
                    visibility=CVDataFactory.visibility_settings(),
                    template_name='modern-0'
                )

            performance = self.run_concurrent_operations(
                generate_pdf,
                count=25,  # Limited by WeasyPrint resources
                max_workers=5
            )

            # PDF generation load should complete
            self.assertGreater(performance['success_rate'], 0.8)  # At least 80% success rate
            self.assertLess(performance['total_time'], 60.0)  # Under 1 minute


@pytest.mark.performance
@apply_test_settings()
class MemoryPerformanceTest(TestCase, PerformanceTestMixin):
    """Test memory usage and optimization"""

    def setUp(self):
        cache.clear()
        gc.collect()  # Clean up before testing

    def tearDown(self):
        cache.clear()
        gc.collect()
    
    def test_memory_usage_with_large_datasets(self):
        """Test memory usage with large datasets"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Create large dataset
        large_cv_data = CVDataFactory.complete_cv_data()
        profiles = []
        
        for i in range(1000):
            profile = CVProfile(
                user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                cv_content=large_cv_data,
                template_name='modern-0'
            )
            profiles.append(profile)
        
        # Bulk create to minimize memory overhead
        CVProfile.objects.bulk_create(profiles, batch_size=100)
        
        current_memory = process.memory_info().rss
        memory_increase = (current_memory - initial_memory) / 1024 / 1024  # MB
        
        # Memory increase should be reasonable
        self.assertLess(memory_increase, 1000)  # Under 1GB
        
        # Clean up and verify memory is freed
        del profiles
        gc.collect()
        
        final_memory = process.memory_info().rss
        memory_after_cleanup = (final_memory - initial_memory) / 1024 / 1024  # MB
        
        # Memory should be largely freed after cleanup
        self.assertLess(memory_after_cleanup, memory_increase * 0.5)
    
    def test_query_memory_optimization(self):
        """Test query memory optimization"""
        # Create test data
        profiles = [CVProfileFactory() for _ in range(500)]
        
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Test iterator vs list memory usage
        start_memory = process.memory_info().rss
        
        # Memory-efficient iteration
        for profile in CVProfile.objects.iterator(chunk_size=100):
            pass  # Process each profile
        
        iterator_memory = process.memory_info().rss
        
        # Memory-inefficient list loading
        all_profiles = list(CVProfile.objects.all())
        
        list_memory = process.memory_info().rss
        
        # Iterator should use less memory
        iterator_increase = iterator_memory - start_memory
        list_increase = list_memory - iterator_memory
        
        # Iterator should use significantly less memory for large datasets
        if len(all_profiles) > 100:
            self.assertLess(iterator_increase, list_increase * 0.5)
        
        del all_profiles
        gc.collect()
    
    def test_json_field_memory_efficiency(self):
        """Test JSON field memory efficiency"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        # Create profiles with large JSON data
        large_json_data = {
            'personalInfo': CVDataFactory.personal_info(),
            'workExperience': CVDataFactory.work_experience(100),  # 100 jobs
            'projects': CVDataFactory.projects(200),  # 200 projects
            'largeTextField': 'A' * 100000,  # 100KB text
        }
        
        profiles = []
        for i in range(100):
            profile = CVProfile(
                user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                cv_content=large_json_data,
                template_name='modern-0'
            )
            profiles.append(profile)
        
        CVProfile.objects.bulk_create(profiles, batch_size=50)
        
        current_memory = process.memory_info().rss
        memory_increase = (current_memory - initial_memory) / 1024 / 1024  # MB
        
        # Memory usage should be reasonable even with large JSON
        self.assertLess(memory_increase, 500)  # Under 500MB
        
        # Test querying large JSON data
        start_query_memory = process.memory_info().rss
        
        # Query without loading full JSON content
        profile_ids = list(CVProfile.objects.values_list('id', flat=True)[:50])
        
        query_memory = process.memory_info().rss
        query_memory_increase = (query_memory - start_query_memory) / 1024 / 1024
        
        # Selective queries should use minimal additional memory
        self.assertLess(query_memory_increase, 50)  # Under 50MB
    
    def test_connection_pool_memory_usage(self):
        """Test database connection pool memory usage"""
        process = psutil.Process()
        initial_memory = process.memory_info().rss
        
        def database_operation():
            """Perform database operation"""
            return CVProfile.objects.count()
        
        # Test many concurrent database connections
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(database_operation) for _ in range(100)]
            results = [future.result() for future in as_completed(futures)]
        
        final_memory = process.memory_info().rss
        memory_increase = (final_memory - initial_memory) / 1024 / 1024  # MB
        
        # Connection pool should not cause excessive memory usage
        self.assertLess(memory_increase, 200)  # Under 200MB
        
        # All operations should succeed
        self.assertEqual(len(results), 100)


@pytest.mark.performance
@pytest.mark.slow
@apply_test_settings()
class LoadTestingScenarios(TransactionTestCase):
    """Comprehensive load testing scenarios"""
    
    def setUp(self):
        cache.clear()
        
    def tearDown(self):
        cache.clear()
    
    def test_user_registration_load(self):
        """Test user registration under load"""
        def register_user():
            """Simulate user registration"""
            user_data = SupabaseUserDataFactory.create_user_data()
            
            with mock_supabase_service() as (service, mock_client):
                from .mocks import setup_mock_supabase_user
                setup_mock_supabase_user(mock_client, 'load-token', user_data)
                
                # Simulate user creation
                user = service.get_or_create_django_user(user_data)
                return user.id
        
        start_time = time.time()
        
        # Simulate 100 concurrent user registrations
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(register_user) for _ in range(100)]
            user_ids = [future.result() for future in as_completed(futures)]
        
        load_time = time.time() - start_time
        
        # Load test should complete efficiently
        self.assertLess(load_time, 30.0)  # Under 30 seconds
        self.assertEqual(len(user_ids), 100)
        self.assertEqual(len(set(user_ids)), 100)  # All unique users
    
    def test_cv_creation_load(self):
        """Test CV creation under load"""
        def create_cv():
            """Simulate CV creation"""
            return CVProfile.objects.create(
                user_id=str(SupabaseUserDataFactory.create_user_data()['id']),
                cv_content=CVDataFactory.complete_cv_data(),
                template_name='modern-0'
            )
        
        start_time = time.time()
        
        # Create 500 CVs concurrently
        with ThreadPoolExecutor(max_workers=15) as executor:
            futures = [executor.submit(create_cv) for _ in range(500)]
            cv_profiles = [future.result() for future in as_completed(futures)]
        
        creation_time = time.time() - start_time
        
        # CV creation should handle load efficiently
        self.assertLess(creation_time, 45.0)  # Under 45 seconds
        self.assertEqual(len(cv_profiles), 500)
    
    @patch('apps.pdf_generation.weasyprint_service.WEASYPRINT_AVAILABLE', True)
    def test_pdf_generation_load(self):
        """Test PDF generation under load"""
        with mock_weasyprint():
            from apps.pdf_generation.weasyprint_service import WeasyPrintService
            
            def generate_pdf():
                """Simulate PDF generation"""
                service = WeasyPrintService()
                return service.generate_pdf(
                    cv_data=CVDataFactory.complete_cv_data(),
                    visibility=CVDataFactory.visibility_settings(),
                    template_name='modern-0'
                )
            
            start_time = time.time()
            
            # Generate 50 PDFs concurrently (limited by WeasyPrint resources)
            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(generate_pdf) for _ in range(50)]
                pdf_results = []
                
                for future in as_completed(futures):
                    try:
                        pdf_bytes = future.result()
                        pdf_results.append(len(pdf_bytes))
                    except Exception as e:
                        pdf_results.append(str(e))
            
            generation_time = time.time() - start_time
            
            # PDF generation load should complete
            self.assertLess(generation_time, 120.0)  # Under 2 minutes
            
            # Most PDFs should generate successfully
            successful_pdfs = [r for r in pdf_results if isinstance(r, int)]
            success_rate = len(successful_pdfs) / len(pdf_results)
            self.assertGreater(success_rate, 0.8)  # At least 80% success rate
    
    def test_mixed_workload_scenario(self):
        """Test mixed workload scenario"""
        results = {
            'users_created': 0,
            'cvs_created': 0,
            'queries_performed': 0,
            'errors': []
        }
        
        def create_user_and_cv():
            """Create user and CV"""
            try:
                user_data = SupabaseUserDataFactory.create_user_data()
                
                # Create CV profile
                cv_profile = CVProfile.objects.create(
                    user_id=user_data['id'],
                    cv_content=CVDataFactory.complete_cv_data(),
                    template_name='modern-0'
                )
                
                results['users_created'] += 1
                results['cvs_created'] += 1
                return cv_profile.id
                
            except Exception as e:
                results['errors'].append(str(e))
                return None
        
        def perform_queries():
            """Perform database queries"""
            try:
                # Various query types
                CVProfile.objects.count()
                CVProfile.objects.filter(template_name='modern-0')[:10]
                CVProfile.objects.order_by('-created_at')[:5]
                
                results['queries_performed'] += 3
                return True
                
            except Exception as e:
                results['errors'].append(str(e))
                return False
        
        start_time = time.time()
        
        # Mixed workload: create operations and read operations
        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit create operations
            create_futures = [executor.submit(create_user_and_cv) for _ in range(100)]
            
            # Submit query operations
            query_futures = [executor.submit(perform_queries) for _ in range(200)]
            
            # Wait for all operations
            all_futures = create_futures + query_futures
            for future in as_completed(all_futures):
                future.result()
        
        total_time = time.time() - start_time
        
        # Mixed workload should complete efficiently
        self.assertLess(total_time, 60.0)  # Under 1 minute
        
        # Most operations should succeed
        self.assertGreater(results['users_created'], 80)  # At least 80 users created
        self.assertGreater(results['cvs_created'], 80)    # At least 80 CVs created
        self.assertGreater(results['queries_performed'], 400)  # At least 400 queries
        
        # Error rate should be low
        error_rate = len(results['errors']) / (results['users_created'] + results['queries_performed'])
        self.assertLess(error_rate, 0.1)  # Less than 10% error rate


@pytest.mark.performance
class BenchmarkTest(TestCase, PerformanceTestMixin):
    """Benchmark tests for performance regression detection"""

    def setUp(self):
        cache.clear()
        self.benchmark_results = {}

    def tearDown(self):
        cache.clear()
        # In a real implementation, you might save benchmark results
        # for comparison across test runs

    def benchmark_operation(self, operation_name, operation_func, iterations=100):
        """Benchmark an operation"""
        times = []

        for _ in range(iterations):
            start_time = time.time()
            operation_func()
            end_time = time.time()
            times.append(end_time - start_time)

        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)

        self.benchmark_results[operation_name] = {
            'avg_time': avg_time,
            'min_time': min_time,
            'max_time': max_time,
            'iterations': iterations
        }

        return avg_time

    def test_cv_creation_benchmark(self):
        """Benchmark CV creation"""
        def create_cv():
            return CVProfileFactory()

        avg_time = self.benchmark_operation('cv_creation', create_cv, 50)

        # CV creation should be fast
        self.assertLess(avg_time, 0.1)  # Under 100ms average

    def test_cv_query_benchmark(self):
        """Benchmark CV queries"""
        # Create test data
        [CVProfileFactory() for _ in range(100)]

        def query_cvs():
            return list(CVProfile.objects.all()[:10])

        avg_time = self.benchmark_operation('cv_query', query_cvs, 100)

        # CV queries should be fast
        self.assertLess(avg_time, 0.05)  # Under 50ms average

    def test_json_field_benchmark(self):
        """Benchmark JSON field operations"""
        # Create profiles with JSON data
        profiles = [CVProfileFactory() for _ in range(50)]

        def json_query():
            return CVProfile.objects.filter(cv_content__has_key='personalInfo')[:10]

        avg_time = self.benchmark_operation('json_query', json_query, 50)

        # JSON queries should be reasonably fast
        self.assertLess(avg_time, 0.1)  # Under 100ms average

    def print_benchmark_results(self):
        """Print benchmark results (for manual inspection)"""
        print("\n=== Benchmark Results ===")
        for operation, results in self.benchmark_results.items():
            print(f"{operation}:")
            print(f"  Average: {results['avg_time']:.4f}s")
            print(f"  Min: {results['min_time']:.4f}s")
            print(f"  Max: {results['max_time']:.4f}s")
            print(f"  Iterations: {results['iterations']}")
            print()