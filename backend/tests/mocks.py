"""
Mock Configurations for External Services

Comprehensive mocking utilities for Supabase, Redis, Celery, WeasyPrint,
and other external dependencies used in testing.
"""

import uuid
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from contextlib import contextmanager

from django.utils import timezone
from django.core.cache import cache
from django.test import override_settings

from apps.authentication.backends import SupabaseService
from .factories import SupabaseUserDataFactory


class MockSupabaseClient:
    """Mock Supabase client for testing authentication flows"""
    
    def __init__(self):
        self.auth = MockSupabaseAuth()
        self.storage = MockSupabaseStorage()
        self.table_data = {}
        self.rpc_responses = {}
    
    def table(self, table_name: str):
        """Mock table operations"""
        return MockSupabaseTable(table_name, self.table_data)
    
    def rpc(self, function_name: str, **params):
        """Mock RPC function calls"""
        return MockSupabaseRPC(function_name, self.rpc_responses, params)


class MockSupabaseAuth:
    """Mock Supabase authentication methods"""
    
    def __init__(self):
        self.users = {}
        self.sessions = {}
        self.token_responses = {}
    
    def get_user(self, token: str):
        """Mock get_user method"""
        response = Mock()
        
        if token in self.token_responses:
            user_data = self.token_responses[token]
            if user_data:
                response.user = Mock()
                response.user.id = user_data['id']
                response.user.email = user_data['email']
                response.user.user_metadata = user_data.get('user_metadata', {})
                response.user.app_metadata = user_data.get('app_metadata', {})
                response.user.phone = user_data.get('phone')
                response.user.email_confirmed_at = user_data.get('email_confirmed_at')
                response.user.created_at = user_data.get('created_at')
                response.user.last_sign_in_at = user_data.get('last_sign_in_at')
            else:
                response.user = None
        else:
            # Default behavior for unknown tokens
            response.user = None
        
        return response
    
    def sign_up(self, email: str, password: str, **kwargs):
        """Mock sign up method"""
        user_id = str(uuid.uuid4())
        user_data = SupabaseUserDataFactory.create_user_data(
            user_id=user_id,
            email=email,
            **kwargs
        )
        self.users[user_id] = user_data
        
        response = Mock()
        response.user = Mock()
        response.user.id = user_id
        response.user.email = email
        response.session = Mock()
        response.session.access_token = f"mock-token-{user_id}"
        
        return response
    
    def sign_in_with_password(self, email: str, password: str):
        """Mock sign in method"""
        # Find user by email
        user_data = None
        for uid, data in self.users.items():
            if data['email'] == email:
                user_data = data
                break
        
        if not user_data:
            raise Exception("Invalid credentials")
        
        response = Mock()
        response.user = Mock()
        response.user.id = user_data['id']
        response.user.email = user_data['email']
        response.session = Mock()
        response.session.access_token = f"mock-token-{user_data['id']}"
        
        return response
    
    def set_mock_user_response(self, token: str, user_data: Optional[Dict[str, Any]]):
        """Set mock response for token verification"""
        self.token_responses[token] = user_data


class MockSupabaseStorage:
    """Mock Supabase storage operations"""
    
    def __init__(self):
        self.buckets = {}
        self.files = {}
    
    def from_(self, bucket_name: str):
        """Mock storage bucket operations"""
        if bucket_name not in self.buckets:
            self.buckets[bucket_name] = {}
        return MockStorageBucket(bucket_name, self.buckets[bucket_name])


class MockStorageBucket:
    """Mock storage bucket operations"""
    
    def __init__(self, bucket_name: str, bucket_data: Dict):
        self.bucket_name = bucket_name
        self.bucket_data = bucket_data
    
    def upload(self, path: str, file_data: bytes, options: Dict = None):
        """Mock file upload"""
        self.bucket_data[path] = {
            'data': file_data,
            'size': len(file_data),
            'uploaded_at': timezone.now().isoformat(),
            'options': options or {}
        }
        
        response = Mock()
        response.data = {
            'path': path,
            'fullPath': f"{self.bucket_name}/{path}",
            'id': str(uuid.uuid4())
        }
        response.error = None
        return response
    
    def get_public_url(self, path: str):
        """Mock get public URL"""
        return f"https://mock-supabase-storage.com/{self.bucket_name}/{path}"
    
    def create_signed_url(self, path: str, expires_in: int):
        """Mock create signed URL"""
        response = Mock()
        response.data = {
            'signed_url': f"https://mock-supabase-storage.com/{self.bucket_name}/{path}?token=mock-signed-token"
        }
        response.error = None
        return response
    
    def remove(self, paths: List[str]):
        """Mock file removal"""
        for path in paths:
            if path in self.bucket_data:
                del self.bucket_data[path]
        
        response = Mock()
        response.data = [{'path': path} for path in paths]
        response.error = None
        return response


class MockSupabaseTable:
    """Mock Supabase table operations"""
    
    def __init__(self, table_name: str, table_data: Dict):
        self.table_name = table_name
        if table_name not in table_data:
            table_data[table_name] = []
        self.data = table_data[table_name]
    
    def select(self, columns: str = "*"):
        """Mock SELECT operations"""
        return MockSupabaseQuery(self.data)
    
    def insert(self, data: Union[Dict, List[Dict]]):
        """Mock INSERT operations"""
        if isinstance(data, dict):
            data = [data]
        
        for item in data:
            if 'id' not in item:
                item['id'] = str(uuid.uuid4())
            item['created_at'] = timezone.now().isoformat()
            self.data.append(item)
        
        response = Mock()
        response.data = data
        response.error = None
        return response
    
    def update(self, data: Dict):
        """Mock UPDATE operations"""
        return MockSupabaseQuery(self.data, update_data=data)
    
    def delete(self):
        """Mock DELETE operations"""
        return MockSupabaseQuery(self.data, delete=True)


class MockSupabaseQuery:
    """Mock Supabase query builder"""
    
    def __init__(self, data: List[Dict], update_data: Dict = None, delete: bool = False):
        self.data = data
        self.update_data = update_data
        self.delete = delete
        self.filters = []
    
    def eq(self, column: str, value: Any):
        """Mock equality filter"""
        self.filters.append(('eq', column, value))
        return self
    
    def neq(self, column: str, value: Any):
        """Mock not equality filter"""
        self.filters.append(('neq', column, value))
        return self
    
    def lt(self, column: str, value: Any):
        """Mock less than filter"""
        self.filters.append(('lt', column, value))
        return self
    
    def gte(self, column: str, value: Any):
        """Mock greater than or equal filter"""
        self.filters.append(('gte', column, value))
        return self
    
    def in_(self, column: str, values: List[Any]):
        """Mock IN filter"""
        self.filters.append(('in', column, values))
        return self
    
    def order(self, column: str, desc: bool = False):
        """Mock ORDER BY"""
        self.order_by = (column, desc)
        return self
    
    def limit(self, count: int):
        """Mock LIMIT"""
        self.limit_count = count
        return self
    
    def execute(self):
        """Execute the mock query"""
        filtered_data = self._apply_filters()
        
        if self.delete:
            # Remove matching items
            for item in filtered_data:
                if item in self.data:
                    self.data.remove(item)
            response = Mock()
            response.data = filtered_data
            response.error = None
            return response
        
        if self.update_data:
            # Update matching items
            for item in filtered_data:
                item.update(self.update_data)
                item['updated_at'] = timezone.now().isoformat()
            
            response = Mock()
            response.data = filtered_data
            response.error = None
            return response
        
        # SELECT operation
        if hasattr(self, 'order_by'):
            column, desc = self.order_by
            filtered_data.sort(
                key=lambda x: x.get(column, ''),
                reverse=desc
            )
        
        if hasattr(self, 'limit_count'):
            filtered_data = filtered_data[:self.limit_count]
        
        response = Mock()
        response.data = filtered_data
        response.error = None
        return response
    
    def _apply_filters(self):
        """Apply filters to data"""
        result = self.data.copy()
        
        for filter_type, column, value in self.filters:
            if filter_type == 'eq':
                result = [item for item in result if item.get(column) == value]
            elif filter_type == 'neq':
                result = [item for item in result if item.get(column) != value]
            elif filter_type == 'lt':
                result = [item for item in result if item.get(column, 0) < value]
            elif filter_type == 'gte':
                result = [item for item in result if item.get(column, 0) >= value]
            elif filter_type == 'in':
                result = [item for item in result if item.get(column) in value]
        
        return result


class MockSupabaseRPC:
    """Mock Supabase RPC operations"""
    
    def __init__(self, function_name: str, responses: Dict, params: Dict):
        self.function_name = function_name
        self.responses = responses
        self.params = params
    
    def execute(self):
        """Execute mock RPC"""
        if self.function_name in self.responses:
            return self.responses[self.function_name]
        
        # Default responses for common functions
        if self.function_name == 'cleanup_expired_pdfs':
            response = Mock()
            response.data = {'deleted_count': 5}
            response.error = None
            return response
        
        # Default empty response
        response = Mock()
        response.data = None
        response.error = None
        return response


class MockRedisClient:
    """Mock Redis client for caching tests"""
    
    def __init__(self):
        self.data = {}
        self.expirations = {}
    
    def get(self, key: str) -> Optional[bytes]:
        """Mock GET operation"""
        if key in self.data:
            # Check expiration
            if key in self.expirations:
                if timezone.now() > self.expirations[key]:
                    del self.data[key]
                    del self.expirations[key]
                    return None
            return self.data[key].encode() if isinstance(self.data[key], str) else self.data[key]
        return None
    
    def set(self, key: str, value: Union[str, bytes], ex: Optional[int] = None) -> bool:
        """Mock SET operation"""
        self.data[key] = value
        if ex:
            self.expirations[key] = timezone.now() + timedelta(seconds=ex)
        return True
    
    def delete(self, *keys: str) -> int:
        """Mock DELETE operation"""
        deleted = 0
        for key in keys:
            if key in self.data:
                del self.data[key]
                deleted += 1
            if key in self.expirations:
                del self.expirations[key]
        return deleted
    
    def exists(self, key: str) -> bool:
        """Mock EXISTS operation"""
        return self.get(key) is not None
    
    def ttl(self, key: str) -> int:
        """Mock TTL operation"""
        if key not in self.data:
            return -2
        if key not in self.expirations:
            return -1
        
        remaining = self.expirations[key] - timezone.now()
        return int(remaining.total_seconds())
    
    def flushdb(self):
        """Mock FLUSHDB operation"""
        self.data.clear()
        self.expirations.clear()


class MockCeleryTask:
    """Mock Celery task for async PDF generation"""
    
    def __init__(self, task_id: str = None):
        self.id = task_id or str(uuid.uuid4())
        self.state = 'PENDING'
        self.result = None
        self.info = {}
        self.retries = 0
        self.max_retries = 3
    
    def delay(self, *args, **kwargs):
        """Mock task.delay()"""
        return self
    
    def apply_async(self, args=None, kwargs=None, **options):
        """Mock task.apply_async()"""
        return self
    
    def get(self, timeout=None):
        """Mock result retrieval"""
        if self.state == 'SUCCESS':
            return self.result
        elif self.state == 'FAILURE':
            raise Exception(self.info.get('error', 'Task failed'))
        elif self.state == 'PENDING':
            if timeout:
                raise Exception('Task timeout')
            return None
        
        return self.result
    
    def ready(self):
        """Check if task is ready"""
        return self.state in ['SUCCESS', 'FAILURE']
    
    def successful(self):
        """Check if task was successful"""
        return self.state == 'SUCCESS'
    
    def failed(self):
        """Check if task failed"""
        return self.state == 'FAILURE'
    
    def retry(self, countdown=None, max_retries=None):
        """Mock task retry"""
        self.retries += 1
        if self.retries >= (max_retries or self.max_retries):
            self.state = 'FAILURE'
            self.info = {'error': 'Max retries exceeded'}
        return self


class MockWeasyPrintDocument:
    """Mock WeasyPrint HTML document"""
    
    def __init__(self, html_content: str):
        self.html_content = html_content
    
    def write_pdf(self, target, stylesheets=None, **kwargs):
        """Mock PDF generation"""
        # Generate fake PDF content
        pdf_content = f"%%PDF-1.4\n%Mock PDF for: {self.html_content[:50]}...\n%%EOF".encode()
        
        if hasattr(target, 'write'):
            target.write(pdf_content)
        else:
            return pdf_content


class MockWeasyPrintCSS:
    """Mock WeasyPrint CSS"""
    
    def __init__(self, string=None, filename=None, **kwargs):
        self.content = string or ""
        self.filename = filename


class MockWeasyPrintFontConfiguration:
    """Mock WeasyPrint font configuration"""
    
    def __init__(self):
        self.fonts = {}
    
    def get_font_size(self, font_family, style, weight, size):
        """Mock font size calculation"""
        return size


# Context managers for testing

@contextmanager
def mock_supabase_service():
    """Context manager for mocking Supabase service"""
    mock_client = MockSupabaseClient()
    
    with patch('apps.authentication.backends.create_client') as mock_create_client:
        mock_create_client.return_value = mock_client
        
        with patch.object(SupabaseService, '__init__', lambda self: None):
            service = SupabaseService()
            service.client = mock_client
            service.admin_client = mock_client
            service.url = 'https://mock.supabase.co'
            service.anon_key = 'mock-anon-key'
            service.service_role_key = 'mock-service-key'
            
            yield service, mock_client


@contextmanager
def mock_redis_cache():
    """Context manager for mocking Redis cache"""
    mock_client = MockRedisClient()
    
    with patch('django.core.cache.cache') as mock_cache:
        mock_cache.get = mock_client.get
        mock_cache.set = mock_client.set
        mock_cache.delete = mock_client.delete
        mock_cache.clear = mock_client.flushdb
        
        yield mock_client


@contextmanager
def mock_celery_tasks():
    """Context manager for mocking Celery tasks"""
    mock_tasks = {}
    
    def create_mock_task(name):
        task = MockCeleryTask()
        mock_tasks[name] = task
        return task
    
    with patch('apps.pdf_generation.tasks.generate_pdf_async') as mock_generate_pdf:
        mock_generate_pdf.delay = lambda *args, **kwargs: create_mock_task('generate_pdf_async')
        mock_generate_pdf.apply_async = lambda *args, **kwargs: create_mock_task('generate_pdf_async')
        
        yield mock_tasks


@contextmanager
def mock_weasyprint():
    """Context manager for mocking WeasyPrint"""
    with patch('apps.pdf_generation.weasyprint_service.HTML') as mock_html:
        with patch('apps.pdf_generation.weasyprint_service.CSS') as mock_css:
            with patch('apps.pdf_generation.weasyprint_service.FontConfiguration') as mock_font:
                mock_html.side_effect = MockWeasyPrintDocument
                mock_css.side_effect = MockWeasyPrintCSS
                mock_font.return_value = MockWeasyPrintFontConfiguration()
                
                yield {
                    'HTML': mock_html,
                    'CSS': mock_css,
                    'FontConfiguration': mock_font
                }


@contextmanager
def mock_file_storage():
    """Context manager for mocking file storage operations"""
    storage_data = {}
    
    def mock_save(self, name, content):
        storage_data[name] = content.read() if hasattr(content, 'read') else content
        return name
    
    def mock_delete(self, name):
        if name in storage_data:
            del storage_data[name]
        return True
    
    def mock_exists(self, name):
        return name in storage_data
    
    def mock_url(self, name):
        return f"https://mock-storage.com/{name}"
    
    with patch('django.core.files.storage.default_storage') as mock_storage:
        mock_storage.save = mock_save
        mock_storage.delete = mock_delete
        mock_storage.exists = mock_exists
        mock_storage.url = mock_url
        
        yield storage_data


# Test settings for mocking external dependencies
TEST_SETTINGS_OVERRIDES = {
    'SUPABASE_URL': 'https://mock.supabase.co',
    'SUPABASE_ANON_KEY': 'mock-anon-key',
    'SUPABASE_SERVICE_ROLE_KEY': 'mock-service-key',
    'SUPABASE_JWT_SECRET': 'mock-jwt-secret',
    'REDIS_URL': 'redis://mock-redis:6379',
    'CELERY_ALWAYS_EAGER': True,
    'CELERY_EAGER_PROPAGATES_EXCEPTIONS': True,
    'CACHES': {
        'default': {
            'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
            'LOCATION': 'test-cache',
        }
    },
    'WEASYPRINT_MAX_WORKERS': 1,
    'WEASYPRINT_TIMEOUT': 30,
    'WEASYPRINT_MAX_MEMORY_MB': 256,
}


def apply_test_settings():
    """Decorator to apply test settings"""
    return override_settings(**TEST_SETTINGS_OVERRIDES)


# Utility functions for test setup

def setup_mock_supabase_user(
    mock_client: MockSupabaseClient,
    token: str,
    user_data: Optional[Dict[str, Any]] = None
):
    """Setup mock Supabase user for authentication tests"""
    if user_data is None:
        user_data = SupabaseUserDataFactory.create_user_data()
    
    mock_client.auth.set_mock_user_response(token, user_data)
    return user_data


def setup_mock_pdf_generation_task(
    mock_tasks: Dict,
    task_name: str = 'generate_pdf_async',
    success: bool = True,
    result: Any = None
):
    """Setup mock PDF generation task"""
    if task_name not in mock_tasks:
        mock_tasks[task_name] = MockCeleryTask()
    
    task = mock_tasks[task_name]
    if success:
        task.state = 'SUCCESS'
        task.result = result or {'file_url': 'https://mock-storage.com/test.pdf'}
    else:
        task.state = 'FAILURE'
        task.info = {'error': 'Mock task failure'}
    
    return task


def create_mock_pdf_content(size: int = 50000) -> bytes:
    """Create mock PDF content for testing"""
    header = b"%PDF-1.4\n"
    content = b"Mock PDF content " * (size // 20)
    footer = b"\n%%EOF"
    return header + content + footer