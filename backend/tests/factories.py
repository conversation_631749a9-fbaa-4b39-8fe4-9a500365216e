"""
Test Data Factories for CVFlo Testing Suite

Comprehensive factories using Factory Boy for generating realistic test data
for CV Builder, PDF Generation, and User Management scenarios.
"""

import uuid
import random
from datetime import datetime, timedelta
from typing import Dict, Any, List

import factory
from factory.django import DjangoModelFactory
from factory.fuzzy import FuzzyChoice, FuzzyDateTime, FuzzyInteger, FuzzyText
from django.contrib.auth.models import User
from django.utils import timezone

from apps.cv_builder.models import CVProfile, PDFGenerationLog, UserProfile, RateLimitLog


class UserFactory(DjangoModelFactory):
    """Factory for Django User model"""
    
    class Meta:
        model = User
        django_get_or_create = ('username',)
    
    username = factory.LazyFunction(lambda: str(uuid.uuid4()))
    email = factory.Faker('email')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_active = True
    is_staff = False
    is_superuser = False
    date_joined = factory.LazyFunction(timezone.now)


class SuperuserFactory(UserFactory):
    """Factory for Django superuser"""
    
    is_staff = True
    is_superuser = True


class UserProfileFactory(DjangoModelFactory):
    """Factory for UserProfile model"""
    
    class Meta:
        model = UserProfile
        django_get_or_create = ('id',)
    
    id = factory.LazyFunction(uuid.uuid4)
    email = factory.Faker('email')
    full_name = factory.LazyAttribute(lambda obj: f"{factory.Faker('first_name').generate({})} {factory.Faker('last_name').generate({})}")
    avatar_url = factory.Faker('image_url')
    preferences = factory.LazyFunction(lambda: {
        'theme': random.choice(['light', 'dark']),
        'notifications': True,
        'language': 'en',
        'timezone': 'UTC'
    })


class CVDataFactory:
    """Factory for generating realistic CV data structures"""
    
    @staticmethod
    def personal_info() -> Dict[str, Any]:
        """Generate personal information section"""
        return {
            'firstName': factory.Faker('first_name').generate({}),
            'lastName': factory.Faker('last_name').generate({}),
            'email': factory.Faker('email').generate({}),
            'phone': factory.Faker('phone_number').generate({}),
            'location': f"{factory.Faker('city').generate({})}, {factory.Faker('state').generate({})}",
            'website': factory.Faker('url').generate({}),
            'linkedin': f"linkedin.com/in/{factory.Faker('user_name').generate({})}",
            'github': f"github.com/{factory.Faker('user_name').generate({})}",
            'summary': factory.Faker('text', max_nb_chars=500).generate({})
        }
    
    @staticmethod
    def work_experience(num_jobs: int = 3) -> List[Dict[str, Any]]:
        """Generate work experience entries"""
        experiences = []
        for i in range(num_jobs):
            start_date = factory.Faker('date_between', start_date='-10y', end_date='-1y').generate({})
            end_date = factory.Faker('date_between', start_date=start_date, end_date='today').generate({}) if i > 0 else None
            
            experiences.append({
                'company': factory.Faker('company').generate({}),
                'position': factory.Faker('job').generate({}),
                'location': f"{factory.Faker('city').generate({})}, {factory.Faker('state').generate({})}",
                'startDate': start_date.strftime('%Y-%m-%d'),
                'endDate': end_date.strftime('%Y-%m-%d') if end_date else None,
                'current': end_date is None,
                'description': factory.Faker('text', max_nb_chars=800).generate({}),
                'achievements': [
                    factory.Faker('sentence').generate({}) for _ in range(random.randint(2, 5))
                ]
            })
        return experiences
    
    @staticmethod
    def education(num_degrees: int = 2) -> List[Dict[str, Any]]:
        """Generate education entries"""
        degrees = ['Bachelor of Science', 'Master of Science', 'Bachelor of Arts', 'Master of Arts', 'PhD']
        fields = ['Computer Science', 'Engineering', 'Business', 'Mathematics', 'Physics', 'Psychology']
        
        education = []
        for i in range(num_degrees):
            graduation_year = factory.Faker('random_int', min=2000, max=2024).generate({})
            
            education.append({
                'institution': factory.Faker('company').generate({}) + ' University',
                'degree': random.choice(degrees),
                'field': random.choice(fields),
                'location': f"{factory.Faker('city').generate({})}, {factory.Faker('state').generate({})}",
                'graduationDate': f"{graduation_year}-05-15",
                'gpa': str(round(random.uniform(3.0, 4.0), 2)),
                'honors': random.choice(['Summa Cum Laude', 'Magna Cum Laude', 'Cum Laude', None]),
                'description': factory.Faker('text', max_nb_chars=300).generate({})
            })
        return education
    
    @staticmethod
    def skills() -> Dict[str, List[str]]:
        """Generate skills sections"""
        technical_skills = ['Python', 'JavaScript', 'React', 'Django', 'PostgreSQL', 'Docker', 'AWS', 'Git']
        soft_skills = ['Leadership', 'Communication', 'Problem Solving', 'Team Collaboration', 'Project Management']
        languages = ['English (Native)', 'Spanish (Fluent)', 'French (Intermediate)', 'German (Basic)']
        
        return {
            'technical': random.sample(technical_skills, random.randint(4, 8)),
            'soft': random.sample(soft_skills, random.randint(3, 5)),
            'languages': random.sample(languages, random.randint(1, 3))
        }
    
    @staticmethod
    def projects(num_projects: int = 3) -> List[Dict[str, Any]]:
        """Generate project entries"""
        projects = []
        for i in range(num_projects):
            projects.append({
                'name': factory.Faker('catch_phrase').generate({}),
                'description': factory.Faker('text', max_nb_chars=400).generate({}),
                'technologies': random.sample(['React', 'Python', 'Django', 'PostgreSQL', 'Docker'], random.randint(2, 4)),
                'url': factory.Faker('url').generate({}),
                'github': f"github.com/{factory.Faker('user_name').generate({})}/{factory.Faker('slug').generate({})}",
                'startDate': factory.Faker('date_between', start_date='-2y', end_date='-6m').generate({}).strftime('%Y-%m-%d'),
                'endDate': factory.Faker('date_between', start_date='-6m', end_date='today').generate({}).strftime('%Y-%m-%d'),
                'highlights': [
                    factory.Faker('sentence').generate({}) for _ in range(random.randint(2, 4))
                ]
            })
        return projects
    
    @staticmethod
    def references(num_refs: int = 2) -> List[Dict[str, Any]]:
        """Generate reference entries"""
        references = []
        for i in range(num_refs):
            references.append({
                'name': f"{factory.Faker('first_name').generate({})} {factory.Faker('last_name').generate({})}",
                'position': factory.Faker('job').generate({}),
                'company': factory.Faker('company').generate({}),
                'email': factory.Faker('email').generate({}),
                'phone': factory.Faker('phone_number').generate({}),
                'relationship': random.choice(['Former Manager', 'Colleague', 'Professor', 'Client'])
            })
        return references
    
    @staticmethod
    def interests() -> List[str]:
        """Generate interests/hobbies"""
        interests_pool = [
            'Photography', 'Travel', 'Hiking', 'Reading', 'Cooking', 'Music',
            'Sports', 'Volunteering', 'Gaming', 'Art', 'Writing', 'Dancing'
        ]
        return random.sample(interests_pool, random.randint(3, 6))
    
    @staticmethod
    def visibility_settings() -> Dict[str, bool]:
        """Generate visibility settings for CV sections"""
        sections = [
            'personalInfo', 'workExperience', 'education', 'skills',
            'projects', 'references', 'interests', 'summary'
        ]
        # Most sections visible by default, some randomly hidden
        return {section: random.choice([True, True, True, False]) for section in sections}
    
    @classmethod
    def complete_cv_data(cls) -> Dict[str, Any]:
        """Generate complete CV data structure"""
        return {
            'personalInfo': cls.personal_info(),
            'workExperience': cls.work_experience(),
            'education': cls.education(),
            'skills': cls.skills(),
            'projects': cls.projects(),
            'references': cls.references(),
            'interests': cls.interests(),
            'visibility': cls.visibility_settings()
        }


class CVProfileFactory(DjangoModelFactory):
    """Factory for CVProfile model"""
    
    class Meta:
        model = CVProfile
        django_get_or_create = ('id',)
    
    id = factory.LazyFunction(uuid.uuid4)
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    cv_content = factory.LazyFunction(CVDataFactory.complete_cv_data)
    template_name = FuzzyChoice(['classic-0', 'modern-0', 'modern-1', 'academic-0'])
    created_at = FuzzyDateTime(
        start_dt=timezone.now() - timedelta(days=365),
        end_dt=timezone.now()
    )
    updated_at = factory.LazyAttribute(lambda obj: obj.created_at + timedelta(days=random.randint(0, 30)))


class PDFGenerationLogFactory(DjangoModelFactory):
    """Factory for PDFGenerationLog model"""
    
    class Meta:
        model = PDFGenerationLog
        django_get_or_create = ('id',)
    
    id = factory.LazyFunction(uuid.uuid4)
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    cv_profile = factory.SubFactory(CVProfileFactory)
    template_name = factory.LazyAttribute(lambda obj: obj.cv_profile.template_name)
    status = FuzzyChoice(['pending', 'processing', 'completed', 'failed', 'cancelled'])
    priority = FuzzyChoice(['low', 'normal', 'high', 'urgent'])
    task_id = factory.LazyFunction(lambda: f"celery-task-{uuid.uuid4()}")
    file_url = factory.Maybe(
        'status',
        yes_declaration=factory.Faker('url'),
        no_declaration=None,
        condition=lambda obj: obj.status == 'completed'
    )
    file_path = factory.Maybe(
        'status',
        yes_declaration=factory.LazyFunction(lambda: f"pdfs/{uuid.uuid4()}.pdf"),
        no_declaration=None,
        condition=lambda obj: obj.status == 'completed'
    )
    file_size = factory.Maybe(
        'status',
        yes_declaration=FuzzyInteger(50000, 2000000),  # 50KB to 2MB
        no_declaration=None,
        condition=lambda obj: obj.status == 'completed'
    )
    processing_started_at = factory.Maybe(
        'status',
        yes_declaration=FuzzyDateTime(
            start_dt=timezone.now() - timedelta(hours=24),
            end_dt=timezone.now() - timedelta(minutes=1)
        ),
        no_declaration=None,
        condition=lambda obj: obj.status in ['processing', 'completed', 'failed']
    )
    processing_completed_at = factory.Maybe(
        'status',
        yes_declaration=factory.LazyAttribute(
            lambda obj: obj.processing_started_at + timedelta(seconds=random.randint(5, 120))
            if obj.processing_started_at else None
        ),
        no_declaration=None,
        condition=lambda obj: obj.status in ['completed', 'failed']
    )
    processing_duration = factory.LazyAttribute(
        lambda obj: obj.processing_completed_at - obj.processing_started_at
        if obj.processing_completed_at and obj.processing_started_at else None
    )
    error_message = factory.Maybe(
        'status',
        yes_declaration=factory.Faker('sentence'),
        no_declaration=None,
        condition=lambda obj: obj.status == 'failed'
    )
    retry_count = factory.Maybe(
        'status',
        yes_declaration=FuzzyInteger(0, 3),
        no_declaration=0,
        condition=lambda obj: obj.status == 'failed'
    )
    max_retries = 3
    metadata = factory.LazyFunction(lambda: {
        'user_agent': factory.Faker('user_agent').generate({}),
        'ip_address': factory.Faker('ipv4').generate({}),
        'source': random.choice(['web', 'api', 'mobile']),
        'version': '1.0.0'
    })
    expires_at = factory.LazyAttribute(
        lambda obj: obj.created_at + timedelta(days=7)
    )


class RateLimitLogFactory(DjangoModelFactory):
    """Factory for RateLimitLog model"""
    
    class Meta:
        model = RateLimitLog
        django_get_or_create = ('id',)
    
    id = factory.LazyFunction(uuid.uuid4)
    user_id = factory.LazyFunction(lambda: uuid.uuid4())
    action = FuzzyChoice(['pdf_generation', 'cv_update', 'template_change'])
    ip_address = factory.Faker('ipv4')
    user_agent = factory.Faker('user_agent')
    endpoint = FuzzyChoice(['/api/pdf/generate/', '/api/cv/data/', '/api/templates/'])
    request_data = factory.LazyFunction(lambda: {
        'template_name': random.choice(['classic-0', 'modern-0', 'academic-0']),
        'timestamp': timezone.now().isoformat(),
        'request_size': random.randint(1000, 50000)
    })
    created_at = FuzzyDateTime(
        start_dt=timezone.now() - timedelta(hours=24),
        end_dt=timezone.now()
    )


class SupabaseUserDataFactory:
    """Factory for Supabase user data structures used in authentication tests"""
    
    @staticmethod
    def create_user_data(
        user_id: str = None,
        email: str = None,
        email_verified: bool = True,
        **kwargs
    ) -> Dict[str, Any]:
        """Create Supabase user data structure"""
        if not user_id:
            user_id = str(uuid.uuid4())
        if not email:
            email = factory.Faker('email').generate({})
        
        first_name = kwargs.get('first_name', factory.Faker('first_name').generate({}))
        last_name = kwargs.get('last_name', factory.Faker('last_name').generate({}))
        
        return {
            'id': user_id,
            'email': email,
            'user_metadata': {
                'full_name': f"{first_name} {last_name}",
                'first_name': first_name,
                'last_name': last_name,
                'avatar_url': kwargs.get('avatar_url', factory.Faker('image_url').generate({}))
            },
            'app_metadata': kwargs.get('app_metadata', {}),
            'phone': kwargs.get('phone', factory.Faker('phone_number').generate({})),
            'email_verified': email_verified,
            'created_at': kwargs.get('created_at', timezone.now().isoformat()),
            'last_sign_in_at': kwargs.get('last_sign_in_at', timezone.now().isoformat()),
        }
    
    @staticmethod
    def create_jwt_token_payload(user_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create JWT token payload for testing"""
        now = timezone.now()
        return {
            'sub': user_data['id'],
            'email': user_data['email'],
            'phone': user_data.get('phone'),
            'app_metadata': user_data.get('app_metadata', {}),
            'user_metadata': user_data.get('user_metadata', {}),
            'role': 'authenticated',
            'aal': 'aal1',
            'amr': [{'method': 'password', 'timestamp': int(now.timestamp())}],
            'session_id': str(uuid.uuid4()),
            'iat': int(now.timestamp()),
            'exp': int((now + timedelta(hours=1)).timestamp()),
            'aud': 'authenticated',
            'iss': 'https://your-project.supabase.co/auth/v1'
        }


class MockDataGenerator:
    """Utility class for generating mock data for various test scenarios"""
    
    @staticmethod
    def generate_pdf_test_scenarios() -> List[Dict[str, Any]]:
        """Generate test scenarios for PDF generation"""
        scenarios = []
        
        # Minimal CV data
        scenarios.append({
            'name': 'minimal_cv',
            'cv_data': {
                'personalInfo': CVDataFactory.personal_info(),
                'visibility': {'personalInfo': True}
            },
            'template': 'classic-0',
            'expected_size_range': (10000, 100000)  # 10KB - 100KB
        })
        
        # Full CV data
        scenarios.append({
            'name': 'full_cv',
            'cv_data': CVDataFactory.complete_cv_data(),
            'template': 'modern-0',
            'expected_size_range': (50000, 500000)  # 50KB - 500KB
        })
        
        # Large CV data (stress test)
        large_cv = CVDataFactory.complete_cv_data()
        large_cv['workExperience'] = CVDataFactory.work_experience(10)  # 10 jobs
        large_cv['projects'] = CVDataFactory.projects(15)  # 15 projects
        scenarios.append({
            'name': 'large_cv',
            'cv_data': large_cv,
            'template': 'academic-0',
            'expected_size_range': (100000, 1000000)  # 100KB - 1MB
        })
        
        return scenarios
    
    @staticmethod
    def generate_auth_test_scenarios() -> List[Dict[str, Any]]:
        """Generate authentication test scenarios"""
        return [
            {
                'name': 'valid_user',
                'user_data': SupabaseUserDataFactory.create_user_data(),
                'should_authenticate': True
            },
            {
                'name': 'unverified_email',
                'user_data': SupabaseUserDataFactory.create_user_data(email_verified=False),
                'should_authenticate': False
            },
            {
                'name': 'missing_metadata',
                'user_data': SupabaseUserDataFactory.create_user_data(),
                'should_authenticate': True
            },
        ]
    
    @staticmethod
    def generate_load_test_data(num_users: int = 100) -> List[Dict[str, Any]]:
        """Generate data for load testing"""
        users = []
        for i in range(num_users):
            user_data = SupabaseUserDataFactory.create_user_data(
                email=f"loadtest{i}@example.com"
            )
            cv_data = CVDataFactory.complete_cv_data()
            
            users.append({
                'user_data': user_data,
                'cv_data': cv_data,
                'template': random.choice(['classic-0', 'modern-0', 'modern-1', 'academic-0'])
            })
        
        return users


# Convenience functions for test setup
def create_test_user_with_cv(
    user_id: str = None,
    email: str = None,
    template: str = 'classic-0'
) -> tuple:
    """Create a test user with associated CV profile"""
    if not user_id:
        user_id = str(uuid.uuid4())
    if not email:
        email = factory.Faker('email').generate({})
    
    user = UserFactory(username=user_id, email=email)
    cv_profile = CVProfileFactory(user_id=user.id, template_name=template)
    
    return user, cv_profile


def create_test_pdf_generation(
    user_id: str = None,
    status: str = 'completed'
) -> PDFGenerationLog:
    """Create a test PDF generation log"""
    if not user_id:
        user_id = str(uuid.uuid4())
    
    cv_profile = CVProfileFactory(user_id=user_id)
    return PDFGenerationLogFactory(
        user_id=user_id,
        cv_profile=cv_profile,
        status=status
    )


def create_supabase_test_data(user_id: str = None) -> Dict[str, Any]:
    """Create Supabase user data for authentication tests"""
    return SupabaseUserDataFactory.create_user_data(user_id=user_id)