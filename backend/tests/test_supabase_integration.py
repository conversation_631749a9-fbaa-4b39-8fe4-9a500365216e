"""
Test Supabase Integration

This module tests the Supabase authentication and database integration,
ensuring proper JWT token validation, user synchronization, and UUID field compatibility.
"""

import pytest
import jwt
import uuid
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from django.conf import settings
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.test import APIRequestFactory

from apps.authentication.backends import (
    SupabaseService,
    SupabaseAuthentication,
    SupabaseBackend,
    OptionalSupabaseAuthentication
)
from apps.cv_builder.models import CVProfile, UserProfile


class SupabaseServiceTests(TestCase):
    """Test SupabaseService functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.supabase_user_data = {
            'id': str(uuid.uuid4()),
            'email': '<EMAIL>',
            'user_metadata': {
                'full_name': '<PERSON>',
                'first_name': '<PERSON>',
                'last_name': '<PERSON><PERSON>'
            },
            'app_metadata': {}
        }
        
    @patch('apps.authentication.backends.create_client')
    def test_supabase_service_initialization(self, mock_create_client):
        """Test SupabaseService initialization with proper configuration"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        # Test with valid configuration
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-anon-key'):
                with patch.object(settings, 'SUPABASE_SERVICE_ROLE_KEY', 'test-service-key'):
                    service = SupabaseService()
                    
                    self.assertEqual(service.url, 'https://test.supabase.co')
                    self.assertEqual(service.anon_key, 'test-anon-key')
                    self.assertEqual(service.service_role_key, 'test-service-key')
                    self.assertEqual(mock_create_client.call_count, 2)  # client and admin_client
    
    @patch('apps.authentication.backends.create_client')
    def test_supabase_service_missing_config(self, mock_create_client):
        """Test SupabaseService initialization with missing configuration"""
        with patch.object(settings, 'SUPABASE_URL', ''):
            with patch.object(settings, 'SUPABASE_ANON_KEY', ''):
                with self.assertRaises(ValueError) as context:
                    SupabaseService()
                
                self.assertIn('Supabase URL and ANON_KEY must be configured', str(context.exception))
    
    @patch('apps.authentication.backends.create_client')
    def test_verify_token_success(self, mock_create_client):
        """Test successful token verification"""
        mock_client = Mock()
        mock_user = Mock()
        mock_user.id = self.supabase_user_data['id']
        mock_user.email = self.supabase_user_data['email']
        mock_user.user_metadata = self.supabase_user_data['user_metadata']
        mock_user.app_metadata = self.supabase_user_data['app_metadata']
        
        mock_response = Mock()
        mock_response.user = mock_user
        mock_client.auth.get_user.return_value = mock_response
        mock_create_client.return_value = mock_client
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                result = service.verify_token('valid-token')
                
                self.assertEqual(result['id'], self.supabase_user_data['id'])
                self.assertEqual(result['email'], self.supabase_user_data['email'])
                self.assertEqual(result['user_metadata'], self.supabase_user_data['user_metadata'])
                mock_client.auth.get_user.assert_called_once_with('valid-token')
    
    @patch('apps.authentication.backends.create_client')
    def test_verify_token_invalid(self, mock_create_client):
        """Test token verification with invalid token"""
        mock_client = Mock()
        mock_response = Mock()
        mock_response.user = None
        mock_client.auth.get_user.return_value = mock_response
        mock_create_client.return_value = mock_client
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                
                with self.assertRaises(AuthenticationFailed) as context:
                    service.verify_token('invalid-token')
                
                self.assertIn('Invalid token', str(context.exception))
    
    @patch('apps.authentication.backends.create_client')
    def test_verify_token_exception(self, mock_create_client):
        """Test token verification with exception"""
        mock_client = Mock()
        mock_client.auth.get_user.side_effect = Exception('Network error')
        mock_create_client.return_value = mock_client
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                
                with self.assertRaises(AuthenticationFailed) as context:
                    service.verify_token('error-token')
                
                self.assertIn('Invalid or expired token', str(context.exception))


@pytest.mark.django_db
class TestSupabaseUserSynchronization:
    """Test user synchronization between Django and Supabase"""
    
    def setup_method(self):
        """Set up test data"""
        self.supabase_user_data = {
            'id': str(uuid.uuid4()),
            'email': '<EMAIL>',
            'user_metadata': {
                'full_name': 'John Doe',
                'first_name': 'John',
                'last_name': 'Doe'
            },
            'app_metadata': {}
        }
    
    @patch('apps.authentication.backends.create_client')
    def test_get_or_create_django_user_new(self, mock_create_client):
        """Test creating new Django user from Supabase data"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                user = service.get_or_create_django_user(self.supabase_user_data)
                
                assert user.username == self.supabase_user_data['id']
                assert user.email == self.supabase_user_data['email']
                assert user.first_name == 'John'
                assert user.last_name == 'Doe'
                
                # Verify user was created in database
                db_user = User.objects.get(username=self.supabase_user_data['id'])
                assert db_user == user
    
    @patch('apps.authentication.backends.create_client')
    def test_get_or_create_django_user_existing_by_username(self, mock_create_client):
        """Test finding existing Django user by username (Supabase ID)"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        # Create existing user
        existing_user = User.objects.create_user(
            username=self.supabase_user_data['id'],
            email=self.supabase_user_data['email'],
            first_name='Jane',
            last_name='Smith'
        )
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                user = service.get_or_create_django_user(self.supabase_user_data)
                
                # Should return existing user
                assert user == existing_user
                assert user.first_name == 'Jane'  # Should not overwrite existing data
                assert User.objects.count() == 1
    
    @patch('apps.authentication.backends.create_client')
    def test_get_or_create_django_user_existing_by_email(self, mock_create_client):
        """Test finding existing Django user by email and updating username"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        # Create existing user with different username
        existing_user = User.objects.create_user(
            username='old-username',
            email=self.supabase_user_data['email'],
            first_name='Jane',
            last_name='Smith'
        )
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                user = service.get_or_create_django_user(self.supabase_user_data)
                
                # Should return existing user with updated username
                assert user == existing_user
                assert user.username == self.supabase_user_data['id']
                assert user.first_name == 'Jane'  # Should not overwrite existing data
                assert User.objects.count() == 1
    
    @patch('apps.authentication.backends.create_client')
    def test_get_or_create_django_user_name_parsing(self, mock_create_client):
        """Test parsing full_name when first_name/last_name not provided"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        supabase_user_data = {
            'id': str(uuid.uuid4()),
            'email': '<EMAIL>',
            'user_metadata': {
                'full_name': 'Alice Johnson Smith'
            },
            'app_metadata': {}
        }
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                service = SupabaseService()
                user = service.get_or_create_django_user(supabase_user_data)
                
                assert user.first_name == 'Alice'
                assert user.last_name == 'Johnson Smith'


class SupabaseAuthenticationTests(TestCase):
    """Test SupabaseAuthentication DRF class"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = APIRequestFactory()
        self.auth = SupabaseAuthentication()
        self.supabase_user_data = {
            'id': str(uuid.uuid4()),
            'email': '<EMAIL>',
            'user_metadata': {'full_name': 'John Doe'},
            'app_metadata': {}
        }
    
    def test_extract_token_valid_bearer(self):
        """Test extracting valid Bearer token"""
        token = self.auth.extract_token('Bearer valid-token-here')
        self.assertEqual(token, 'valid-token-here')
    
    def test_extract_token_invalid_format(self):
        """Test extracting token with invalid format"""
        # Missing Bearer prefix
        token = self.auth.extract_token('valid-token-here')
        self.assertIsNone(token)
        
        # Wrong prefix
        token = self.auth.extract_token('Basic dXNlcjpwYXNz')
        self.assertIsNone(token)
        
        # Too many parts
        token = self.auth.extract_token('Bearer token extra part')
        self.assertIsNone(token)
    
    def test_extract_token_empty(self):
        """Test extracting token with empty/None header"""
        token = self.auth.extract_token('')
        self.assertIsNone(token)
        
        token = self.auth.extract_token(None)
        self.assertIsNone(token)
    
    @patch.object(SupabaseService, 'verify_token')
    @patch.object(SupabaseService, 'get_or_create_django_user')
    def test_authenticate_success(self, mock_get_user, mock_verify_token):
        """Test successful authentication"""
        mock_verify_token.return_value = self.supabase_user_data
        mock_user = User.objects.create_user(
            username=self.supabase_user_data['id'],
            email=self.supabase_user_data['email']
        )
        mock_get_user.return_value = mock_user
        
        request = self.factory.get('/', HTTP_AUTHORIZATION='Bearer valid-token')
        result = self.auth.authenticate(request)
        
        self.assertIsNotNone(result)
        user, token = result
        self.assertEqual(user, mock_user)
        self.assertEqual(token, 'valid-token')
        self.assertEqual(request.supabase_user, self.supabase_user_data)
    
    def test_authenticate_no_header(self):
        """Test authentication with no Authorization header"""
        request = self.factory.get('/')
        result = self.auth.authenticate(request)
        self.assertIsNone(result)
    
    @patch.object(SupabaseService, 'verify_token')
    def test_authenticate_invalid_token(self, mock_verify_token):
        """Test authentication with invalid token"""
        mock_verify_token.side_effect = AuthenticationFailed('Invalid token')
        
        request = self.factory.get('/', HTTP_AUTHORIZATION='Bearer invalid-token')
        
        with self.assertRaises(AuthenticationFailed):
            self.auth.authenticate(request)
    
    def test_authenticate_header_response(self):
        """Test authenticate_header method"""
        request = self.factory.get('/')
        header = self.auth.authenticate_header(request)
        self.assertEqual(header, 'Bearer')


class OptionalSupabaseAuthenticationTests(TestCase):
    """Test OptionalSupabaseAuthentication class"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = APIRequestFactory()
        self.auth = OptionalSupabaseAuthentication()
    
    @patch.object(SupabaseService, 'verify_token')
    def test_optional_auth_no_token(self, mock_verify_token):
        """Test optional authentication returns None instead of raising exception"""
        mock_verify_token.side_effect = AuthenticationFailed('Invalid token')
        
        request = self.factory.get('/', HTTP_AUTHORIZATION='Bearer invalid-token')
        result = self.auth.authenticate(request)
        
        # Should return None instead of raising exception
        self.assertIsNone(result)


@pytest.mark.django_db
class TestUUIDFieldCompatibility:
    """Test UUID field compatibility with Supabase auth.users"""
    
    def test_cv_profile_uuid_field(self):
        """Test CVProfile UUID field creation and validation"""
        user = User.objects.create_user(
            username=str(uuid.uuid4()),
            email='<EMAIL>'
        )
        
        cv_profile = CVProfile.objects.create(
            user_id=user.id,
            template_name='classic-0',
            cv_content={'personalInfo': {'name': 'Test User'}}
        )
        
        # Verify UUID field is properly set
        assert isinstance(cv_profile.id, uuid.UUID)
        assert cv_profile.user_id == user.id
        
        # Verify we can query by UUID
        retrieved = CVProfile.objects.get(id=cv_profile.id)
        assert retrieved == cv_profile
    
    def test_user_profile_uuid_field(self):
        """Test UserProfile UUID field creation and validation"""
        user_profile = UserProfile.objects.create(
            email='<EMAIL>'
        )
        
        # Verify UUID field is properly set
        assert isinstance(user_profile.id, uuid.UUID)
        assert user_profile.email == '<EMAIL>'
        
        # Verify we can query by UUID
        retrieved = UserProfile.objects.get(id=user_profile.id)
        assert retrieved == user_profile
    
    def test_uuid_serialization(self):
        """Test UUID serialization for API responses"""
        user = User.objects.create_user(
            username=str(uuid.uuid4()),
            email='<EMAIL>'
        )
        
        cv_profile = CVProfile.objects.create(
            user_id=user.id,
            template_name='modern-0'
        )
        
        # Test that UUID converts to string properly
        uuid_str = str(cv_profile.id)
        assert len(uuid_str) == 36  # Standard UUID string length
        assert '-' in uuid_str
        
        # Test that we can parse it back
        parsed_uuid = uuid.UUID(uuid_str)
        assert parsed_uuid == cv_profile.id


class SupabaseBackendTests(TestCase):
    """Test SupabaseBackend for Django authentication"""
    
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        self.backend = SupabaseBackend()
        self.supabase_user_data = {
            'id': str(uuid.uuid4()),
            'email': '<EMAIL>',
            'user_metadata': {'full_name': 'John Doe'},
            'app_metadata': {}
        }
    
    @patch.object(SupabaseService, 'verify_token')
    @patch.object(SupabaseService, 'get_or_create_django_user')
    def test_authenticate_success(self, mock_get_user, mock_verify_token):
        """Test successful authentication via backend"""
        mock_verify_token.return_value = self.supabase_user_data
        mock_user = User.objects.create_user(
            username=self.supabase_user_data['id'],
            email=self.supabase_user_data['email']
        )
        mock_get_user.return_value = mock_user
        
        request = self.factory.get('/')
        user = self.backend.authenticate(request, token='valid-token')
        
        self.assertEqual(user, mock_user)
    
    def test_authenticate_no_token(self):
        """Test authentication with no token"""
        request = self.factory.get('/')
        user = self.backend.authenticate(request)
        self.assertIsNone(user)
    
    @patch.object(SupabaseService, 'verify_token')
    def test_authenticate_invalid_token(self, mock_verify_token):
        """Test authentication with invalid token"""
        mock_verify_token.side_effect = AuthenticationFailed('Invalid token')
        
        request = self.factory.get('/')
        user = self.backend.authenticate(request, token='invalid-token')
        self.assertIsNone(user)
    
    def test_get_user_existing(self):
        """Test getting existing user by ID"""
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>'
        )
        
        retrieved_user = self.backend.get_user(user.id)
        self.assertEqual(retrieved_user, user)
    
    def test_get_user_nonexistent(self):
        """Test getting non-existent user by ID"""
        retrieved_user = self.backend.get_user(99999)
        self.assertIsNone(retrieved_user)


@pytest.mark.django_db
class TestSupabaseIntegrationEndToEnd:
    """End-to-end integration tests"""
    
    def setup_method(self):
        """Set up test data"""
        self.factory = APIRequestFactory()
        self.supabase_user_data = {
            'id': str(uuid.uuid4()),
            'email': '<EMAIL>',
            'user_metadata': {
                'full_name': 'Integration Test User',
                'first_name': 'Integration',
                'last_name': 'User'
            },
            'app_metadata': {}
        }
    
    @patch('apps.authentication.backends.create_client')
    def test_full_authentication_flow(self, mock_create_client):
        """Test complete authentication flow from token to user creation"""
        # Mock Supabase client
        mock_client = Mock()
        mock_user = Mock()
        mock_user.id = self.supabase_user_data['id']
        mock_user.email = self.supabase_user_data['email']
        mock_user.user_metadata = self.supabase_user_data['user_metadata']
        mock_user.app_metadata = self.supabase_user_data['app_metadata']
        
        mock_response = Mock()
        mock_response.user = mock_user
        mock_client.auth.get_user.return_value = mock_response
        mock_create_client.return_value = mock_client
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                # Test authentication
                auth = SupabaseAuthentication()
                request = self.factory.get('/', HTTP_AUTHORIZATION='Bearer valid-token')
                
                user, token = auth.authenticate(request)
                
                # Verify user was created properly
                assert user.username == self.supabase_user_data['id']
                assert user.email == self.supabase_user_data['email']
                assert user.first_name == 'Integration'
                assert user.last_name == 'User'
                assert token == 'valid-token'
                
                # Verify Supabase user data is attached to request
                assert request.supabase_user == self.supabase_user_data
                
                # Verify user exists in database
                db_user = User.objects.get(username=self.supabase_user_data['id'])
                assert db_user == user
    
    @patch('apps.authentication.backends.create_client')
    def test_cv_profile_creation_with_supabase_user(self, mock_create_client):
        """Test creating CV profile with Supabase authenticated user"""
        mock_client = Mock()
        mock_create_client.return_value = mock_client
        
        with patch.object(settings, 'SUPABASE_URL', 'https://test.supabase.co'):
            with patch.object(settings, 'SUPABASE_ANON_KEY', 'test-key'):
                # Create Django user as if authenticated via Supabase
                user = User.objects.create_user(
                    username=self.supabase_user_data['id'],  # Supabase UUID as username
                    email=self.supabase_user_data['email'],
                    first_name='Integration',
                    last_name='User'
                )
                
                # Create CV profile using Django user ID
                cv_profile = CVProfile.objects.create(
                    user_id=user.id,
                    template_name='modern-0',
                    cv_content={
                        'personalInfo': {
                            'firstName': 'Integration',
                            'lastName': 'User',
                            'email': '<EMAIL>'
                        },
                        'visibility': {
                            'personalInfo': True
                        }
                    }
                )
                
                # Verify CV profile has UUID primary key
                assert isinstance(cv_profile.id, uuid.UUID)
                
                # Verify we can retrieve CV profile by user_id
                retrieved = CVProfile.objects.get(user_id=user.id)
                assert retrieved == cv_profile
                
                # Verify content is properly stored
                assert retrieved.cv_content['personalInfo']['firstName'] == 'Integration'
                assert retrieved.template_name == 'modern-0'