# Production-ready Docker configuration for WeasyPrint PDF generation
# This Dockerfile includes all necessary system dependencies for WeasyPrint

FROM python:3.11-slim-bullseye

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies for WeasyPrint
RUN apt-get update && apt-get install -y \
    # WeasyPrint dependencies
    libcairo2-dev \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    libxml2-dev \
    libxslt-dev \
    # Font support
    fontconfig \
    fonts-dejavu-core \
    fonts-liberation \
    fonts-noto \
    # Build dependencies
    gcc \
    g++ \
    pkg-config \
    # Additional utilities
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create non-root user
RUN groupadd -r django && useradd -r -g django django

# Copy requirements first (for better caching)
COPY requirements.txt .
COPY requirements-weasyprint.txt .

# Install Python dependencies
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-weasyprint.txt

# Copy application code
COPY . .

# Set proper permissions
RUN chown -R django:django /app
USER django

# Create necessary directories
RUN mkdir -p /app/media /app/static /app/logs

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD python manage.py check || exit 1

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:8000", "--workers", "4", "--worker-class", "sync", "--timeout", "120", "cvflo.wsgi:application"]