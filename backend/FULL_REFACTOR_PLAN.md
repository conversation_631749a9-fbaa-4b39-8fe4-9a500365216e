# CVFlo Backend Full Refactor Plan - SOLID & OOP Architecture

## Current Architecture Problems

### Violations of SOLID Principles
1. **Single Responsibility Principle (SRP)**: Classes doing multiple things
2. **Open/Closed Principle (OCP)**: Hard to extend without modifying existing code
3. **Liskov Substitution Principle (LSP)**: Inconsistent interfaces
4. **Interface Segregation Principle (ISP)**: Fat interfaces, unused methods
5. **Dependency Inversion Principle (DIP)**: High-level modules depend on low-level modules

### Current Issues
- Security logic scattered across 4+ files
- Direct database access in views
- Tight coupling between components
- No proper abstraction layers
- Difficult to test and mock
- Business logic mixed with infrastructure concerns

## Proposed Architecture - Clean Architecture + SOLID

```
backend/
├── apps/
│   ├── core/
│   │   ├── domain/                    # Domain Layer (Business Logic)
│   │   │   ├── entities/              # Domain Entities
│   │   │   ├── value_objects/         # Value Objects
│   │   │   ├── services/              # Domain Services
│   │   │   └── repositories/          # Repository Interfaces
│   │   ├── application/               # Application Layer (Use Cases)
│   │   │   ├── use_cases/             # Application Use Cases
│   │   │   ├── dto/                   # Data Transfer Objects
│   │   │   └── interfaces/            # Application Interfaces
│   │   ├── infrastructure/            # Infrastructure Layer
│   │   │   ├── persistence/           # Database Implementation
│   │   │   ├── external_services/     # External APIs (Supabase, etc.)
│   │   │   ├── security/              # Security Implementation
│   │   │   └── messaging/             # Event handling
│   │   └── presentation/              # Presentation Layer
│   │       ├── api/                   # REST API Controllers
│   │       ├── middleware/            # HTTP Middleware
│   │       ├── serializers/           # Request/Response serializers
│   │       └── validators/            # Input validation
│   ├── authentication/               # Authentication Bounded Context
│   │   ├── domain/
│   │   ├── application/
│   │   ├── infrastructure/
│   │   └── presentation/
│   ├── cv_builder/                   # CV Builder Bounded Context
│   │   ├── domain/
│   │   ├── application/
│   │   ├── infrastructure/
│   │   └── presentation/
│   └── pdf_generation/               # PDF Generation Bounded Context
│       ├── domain/
│       ├── application/
│       ├── infrastructure/
│       └── presentation/
└── shared/                           # Shared Kernel
    ├── domain/
    ├── infrastructure/
    └── presentation/
```

## SOLID Principles Implementation

### 1. Single Responsibility Principle (SRP)
Each class has ONE reason to change:

```python
# ❌ Current: Fat class doing everything
class CVService:
    def create_cv(self): pass
    def validate_cv(self): pass
    def save_cv(self): pass
    def generate_pdf(self): pass
    def authenticate_user(self): pass

# ✅ Refactored: Separate responsibilities
class CVCreationService:
    def create_cv(self): pass

class CVValidationService:
    def validate_cv(self): pass

class CVRepository:
    def save_cv(self): pass

class PDFGenerationService:
    def generate_pdf(self): pass

class AuthenticationService:
    def authenticate_user(self): pass
```

### 2. Open/Closed Principle (OCP)
Open for extension, closed for modification:

```python
# ✅ Abstract base for PDF generators
class PDFGenerator(ABC):
    @abstractmethod
    def generate(self, cv_data: CVData) -> bytes: pass

class WeasyPrintGenerator(PDFGenerator):
    def generate(self, cv_data: CVData) -> bytes: pass

class PuppeteerGenerator(PDFGenerator):  # Easy to add new generators
    def generate(self, cv_data: CVData) -> bytes: pass
```

### 3. Liskov Substitution Principle (LSP)
Subtypes must be substitutable for their base types:

```python
# ✅ All storage implementations follow same contract
class StorageService(ABC):
    @abstractmethod
    def store(self, file_data: bytes, filename: str) -> str: pass

class SupabaseStorage(StorageService):
    def store(self, file_data: bytes, filename: str) -> str: pass

class LocalStorage(StorageService):
    def store(self, file_data: bytes, filename: str) -> str: pass
```

### 4. Interface Segregation Principle (ISP)
Clients shouldn't depend on interfaces they don't use:

```python
# ❌ Fat interface
class UserService:
    def authenticate(self): pass
    def get_profile(self): pass
    def update_profile(self): pass
    def delete_account(self): pass
    def generate_pdf(self): pass  # Not related to user!

# ✅ Segregated interfaces
class AuthenticationService:
    def authenticate(self): pass

class ProfileService:
    def get_profile(self): pass
    def update_profile(self): pass

class AccountService:
    def delete_account(self): pass
```

### 5. Dependency Inversion Principle (DIP)
Depend on abstractions, not concretions:

```python
# ✅ High-level module depends on abstraction
class CVCreationUseCase:
    def __init__(self, 
                 cv_repository: CVRepositoryInterface,
                 validation_service: ValidationServiceInterface,
                 event_publisher: EventPublisherInterface):
        self._cv_repository = cv_repository
        self._validation_service = validation_service
        self._event_publisher = event_publisher
```

## Domain-Driven Design Implementation

### Bounded Contexts
1. **Authentication Context**: User auth, sessions, permissions
2. **CV Builder Context**: CV creation, editing, templates
3. **PDF Generation Context**: PDF creation, storage, templates
4. **User Management Context**: Profiles, preferences, settings

### Domain Entities
```python
# Domain Entity - Rich domain model
class CV:
    def __init__(self, cv_id: CVId, user_id: UserId, template: Template):
        self._id = cv_id
        self._user_id = user_id
        self._template = template
        self._sections: List[CVSection] = []
        self._created_at = datetime.now()
        self._updated_at = datetime.now()
    
    def add_section(self, section: CVSection) -> None:
        if not self._can_add_section(section):
            raise DomainException("Cannot add section")
        self._sections.append(section)
        self._updated_at = datetime.now()
    
    def _can_add_section(self, section: CVSection) -> bool:
        # Business rules
        return len(self._sections) < 10
```

### Value Objects
```python
class CVId:
    def __init__(self, value: str):
        if not self._is_valid_uuid(value):
            raise ValueError("Invalid CV ID")
        self._value = value
    
    @property
    def value(self) -> str:
        return self._value
    
    def __eq__(self, other) -> bool:
        return isinstance(other, CVId) and self._value == other._value
```

### Repository Pattern
```python
class CVRepositoryInterface(ABC):
    @abstractmethod
    def save(self, cv: CV) -> None: pass
    
    @abstractmethod
    def find_by_id(self, cv_id: CVId) -> Optional[CV]: pass
    
    @abstractmethod
    def find_by_user_id(self, user_id: UserId) -> List[CV]: pass

class DjangoCVRepository(CVRepositoryInterface):
    def save(self, cv: CV) -> None:
        # Django ORM implementation
        pass
```

### Use Cases (Application Services)
```python
class CreateCVUseCase:
    def __init__(self, 
                 cv_repository: CVRepositoryInterface,
                 template_service: TemplateServiceInterface,
                 event_publisher: EventPublisherInterface):
        self._cv_repository = cv_repository
        self._template_service = template_service
        self._event_publisher = event_publisher
    
    def execute(self, request: CreateCVRequest) -> CreateCVResponse:
        # 1. Validate request
        # 2. Get template
        # 3. Create CV entity
        # 4. Save CV
        # 5. Publish event
        # 6. Return response
        pass
```

## Security Architecture Refactor

### Consolidated Security Module
```python
# Single security package
apps/core/infrastructure/security/
├── __init__.py
├── authentication/
│   ├── jwt_authenticator.py
│   ├── supabase_authenticator.py
│   └── authentication_service.py
├── authorization/
│   ├── permission_checker.py
│   └── role_based_access.py
├── rate_limiting/
│   ├── rate_limiter.py
│   └── strategies/
└── validation/
    ├── input_validator.py
    └── cv_data_validator.py
```

### Security Interfaces
```python
class AuthenticationServiceInterface(ABC):
    @abstractmethod
    def authenticate(self, token: str) -> AuthenticationResult: pass

class AuthorizationServiceInterface(ABC):
    @abstractmethod
    def authorize(self, user: User, resource: str, action: str) -> bool: pass

class RateLimiterInterface(ABC):
    @abstractmethod
    def is_allowed(self, key: str, limit: int, window: int) -> bool: pass
```

## Migration Strategy

### Phase 1: Foundation (Week 1-2)
1. Create new directory structure
2. Define domain entities and value objects
3. Create repository interfaces
4. Set up dependency injection container

### Phase 2: Core Refactor (Week 3-4)
1. Implement authentication bounded context
2. Refactor security into single module
3. Create use cases for authentication

### Phase 3: CV Builder (Week 5-6)
1. Implement CV builder bounded context
2. Migrate CV creation/editing logic
3. Implement proper domain models

### Phase 4: PDF Generation (Week 7-8)
1. Implement PDF generation bounded context
2. Abstract PDF generators
3. Implement storage abstraction

### Phase 5: Integration & Testing (Week 9-10)
1. Wire everything together
2. Comprehensive testing
3. Performance optimization
4. Documentation

## Benefits of This Architecture

### Maintainability
- ✅ Clear separation of concerns
- ✅ Easy to locate and modify code
- ✅ Consistent patterns throughout

### Testability
- ✅ Easy to mock dependencies
- ✅ Unit tests for domain logic
- ✅ Integration tests for infrastructure

### Scalability
- ✅ Easy to add new features
- ✅ Can scale different bounded contexts independently
- ✅ Clear extension points

### Team Development
- ✅ Multiple developers can work on different contexts
- ✅ Clear interfaces between components
- ✅ Reduced merge conflicts

## Estimated Timeline
- **Total Duration**: 10 weeks
- **Team Size**: 1-2 developers
- **Risk Level**: Medium (well-planned migration)

This refactor will transform your codebase into a professional, enterprise-grade application that follows industry best practices and will be much easier to maintain and extend in the future.

## Implementation Examples

### Before vs After Comparison

#### Current Authentication (Scattered)
```python
# apps/core/security.py - 558 lines
# apps/pdf_generation/security.py - 497 lines
# apps/core/middleware/supabase_security.py - 402 lines
# apps/authentication/backends.py - mixed concerns
```

#### After Authentication (Clean)
```python
# apps/core/domain/entities/user.py
class User:
    def __init__(self, user_id: UserId, email: Email):
        self._id = user_id
        self._email = email
        self._is_active = True

    def deactivate(self) -> None:
        self._is_active = False

# apps/core/application/use_cases/authenticate_user.py
class AuthenticateUserUseCase:
    def __init__(self, auth_service: AuthenticationServiceInterface):
        self._auth_service = auth_service

    def execute(self, request: AuthenticationRequest) -> AuthenticationResponse:
        result = self._auth_service.authenticate(request.token)
        if not result.is_successful:
            raise AuthenticationFailedException()
        return AuthenticationResponse(user=result.user)

# apps/core/infrastructure/security/supabase_authenticator.py
class SupabaseAuthenticator(AuthenticationServiceInterface):
    def authenticate(self, token: str) -> AuthenticationResult:
        # Single, focused implementation
        pass
```

### Dependency Injection Setup
```python
# apps/core/infrastructure/di_container.py
class DIContainer:
    def __init__(self):
        self._services = {}
        self._configure_services()

    def _configure_services(self):
        # Repositories
        self.register(CVRepositoryInterface, DjangoCVRepository)
        self.register(UserRepositoryInterface, DjangoUserRepository)

        # Services
        self.register(AuthenticationServiceInterface, SupabaseAuthenticator)
        self.register(PDFGeneratorInterface, WeasyPrintGenerator)

        # Use Cases
        self.register(CreateCVUseCase, lambda: CreateCVUseCase(
            self.get(CVRepositoryInterface),
            self.get(TemplateServiceInterface),
            self.get(EventPublisherInterface)
        ))
```

### API Controller Example
```python
# apps/cv_builder/presentation/api/cv_controller.py
class CVController(APIView):
    def __init__(self):
        self._create_cv_use_case = container.get(CreateCVUseCase)
        self._get_cv_use_case = container.get(GetCVUseCase)

    def post(self, request):
        try:
            # Convert HTTP request to use case request
            use_case_request = CreateCVRequest.from_http_request(request)

            # Execute use case
            response = self._create_cv_use_case.execute(use_case_request)

            # Convert use case response to HTTP response
            return Response(response.to_dict(), status=201)

        except DomainException as e:
            return Response({'error': str(e)}, status=400)
```

## Next Steps

1. **Review this plan** - Does the architecture align with your vision?
2. **Approve migration strategy** - Are you comfortable with the 10-week timeline?
3. **Choose starting point** - Which bounded context should we tackle first?
4. **Set up development approach** - Feature branches, testing strategy, etc.

The refactor will result in:
- 🏗️ **Clean Architecture** with proper layering
- 🔒 **Consolidated Security** in one place
- 🧪 **100% Testable** code with proper mocking
- 📈 **Scalable** design that grows with your needs
- 👥 **Team-Friendly** structure for multiple developers
- 🚀 **Production-Ready** enterprise-grade codebase
