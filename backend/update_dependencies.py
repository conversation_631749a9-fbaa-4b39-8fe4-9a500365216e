#!/usr/bin/env python3
"""
Dependency Update Script for CVFlo Backend

This script helps keep all requirements files updated with the latest versions.
Run this periodically to avoid tech debt and security vulnerabilities.

Usage:
    python update_dependencies.py [--check-only] [--install]

Options:
    --check-only    Only check for outdated packages, don't update files
    --install       Install updated packages after updating files
"""

import subprocess
import sys
import argparse
import json
from pathlib import Path


def run_command(cmd, capture_output=True):
    """Run a shell command and return the result"""
    try:
        result = subprocess.run(
            cmd, shell=True, capture_output=capture_output, 
            text=True, check=True
        )
        return result.stdout.strip() if capture_output else None
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {cmd}")
        print(f"Error: {e}")
        return None


def get_outdated_packages():
    """Get list of outdated packages"""
    print("🔍 Checking for outdated packages...")
    result = run_command("pip list --outdated --format=json")
    
    if result:
        try:
            outdated = json.loads(result)
            return {pkg['name'].lower(): pkg['latest_version'] for pkg in outdated}
        except json.JSONDecodeError:
            print("❌ Failed to parse pip output")
            return {}
    return {}


def update_requirements_file(file_path, outdated_packages):
    """Update a requirements file with latest versions"""
    if not file_path.exists():
        print(f"⚠️  File not found: {file_path}")
        return False
    
    print(f"📝 Updating {file_path.name}...")
    
    lines = file_path.read_text().splitlines()
    updated_lines = []
    updates_made = 0
    
    for line in lines:
        if line.strip() and not line.strip().startswith('#'):
            # Parse package name from line like "package>=1.0.0"
            if '>=' in line or '==' in line:
                parts = line.split('>=') if '>=' in line else line.split('==')
                if len(parts) == 2:
                    package_name = parts[0].strip().lower()
                    current_version = parts[1].strip()
                    
                    if package_name in outdated_packages:
                        new_version = outdated_packages[package_name]
                        updated_line = f"{parts[0].strip()}>={new_version}"
                        updated_lines.append(updated_line)
                        print(f"  ✅ {package_name}: {current_version} → {new_version}")
                        updates_made += 1
                    else:
                        updated_lines.append(line)
                else:
                    updated_lines.append(line)
            else:
                updated_lines.append(line)
        else:
            updated_lines.append(line)
    
    if updates_made > 0:
        file_path.write_text('\n'.join(updated_lines) + '\n')
        print(f"  📦 Updated {updates_made} packages in {file_path.name}")
    else:
        print(f"  ✨ No updates needed for {file_path.name}")
    
    return updates_made > 0


def main():
    parser = argparse.ArgumentParser(description="Update CVFlo dependencies")
    parser.add_argument('--check-only', action='store_true', 
                       help='Only check for outdated packages')
    parser.add_argument('--install', action='store_true',
                       help='Install updated packages after updating files')
    
    args = parser.parse_args()
    
    # Get current directory
    backend_dir = Path(__file__).parent
    
    # Requirements files to update
    requirements_files = [
        backend_dir / 'requirements.txt',
        backend_dir / 'requirements-test.txt',
        backend_dir / 'requirements-weasyprint.txt'
    ]
    
    print("🚀 CVFlo Dependency Update Tool")
    print("=" * 40)
    
    # Get outdated packages
    outdated = get_outdated_packages()
    
    if not outdated:
        print("✨ All packages are up to date!")
        return
    
    print(f"\n📊 Found {len(outdated)} outdated packages:")
    for pkg, version in outdated.items():
        print(f"  • {pkg} → {version}")
    
    if args.check_only:
        print("\n🔍 Check-only mode: No files will be updated")
        return
    
    print("\n📝 Updating requirements files...")
    
    # Update each requirements file
    any_updates = False
    for req_file in requirements_files:
        if update_requirements_file(req_file, outdated):
            any_updates = True
    
    if any_updates:
        print("\n✅ Requirements files updated!")
        
        if args.install:
            print("\n📦 Installing updated packages...")
            for req_file in requirements_files:
                if req_file.exists():
                    print(f"Installing from {req_file.name}...")
                    run_command(f"pip install -r {req_file}", capture_output=False)
        else:
            print("\n💡 To install updated packages, run:")
            print("   pip install -r requirements.txt")
            print("   pip install -r requirements-weasyprint.txt")
            print("   pip install -r requirements-test.txt")
    else:
        print("\n✨ No updates were needed!")
    
    print("\n🎉 Done!")


if __name__ == "__main__":
    main()
