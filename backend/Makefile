# CVFlo Backend Makefile
# Simplifies common development tasks

.PHONY: help install install-dev install-test install-weasyprint update-deps check-deps clean test

# Default target
help:
	@echo "CVFlo Backend Development Commands"
	@echo "=================================="
	@echo ""
	@echo "Installation:"
	@echo "  install          Install core production dependencies"
	@echo "  install-dev      Install all dependencies (prod + test + weasyprint)"
	@echo "  install-test     Install testing dependencies"
	@echo "  install-weasyprint Install WeasyPrint and PDF generation dependencies"
	@echo ""
	@echo "Dependency Management:"
	@echo "  update-deps      Update all dependencies to latest versions"
	@echo "  check-deps       Check for outdated dependencies"
	@echo "  freeze-deps      Generate current dependency versions"
	@echo ""
	@echo "Development:"
	@echo "  test             Run all tests"
	@echo "  test-fast        Run tests without slow tests"
	@echo "  lint             Run code quality checks"
	@echo "  format           Format code with black and isort"
	@echo "  clean            Clean up temporary files"
	@echo ""
	@echo "Environment:"
	@echo "  venv             Create virtual environment"
	@echo "  requirements     Show all requirements files"

# Installation targets
install:
	@echo "📦 Installing core production dependencies..."
	pip install --upgrade pip setuptools wheel
	pip install -r requirements.txt

install-test:
	@echo "🧪 Installing testing dependencies..."
	pip install -r requirements-test.txt

install-weasyprint:
	@echo "📄 Installing WeasyPrint dependencies..."
	pip install -r requirements-weasyprint.txt

install-dev: install install-weasyprint install-test
	@echo "🚀 All dependencies installed!"

# Dependency management
update-deps:
	@echo "🔄 Updating dependencies to latest versions..."
	python update_dependencies.py --install

check-deps:
	@echo "🔍 Checking for outdated dependencies..."
	python update_dependencies.py --check-only

check-versions:
	@echo "🔍 Checking package versions against PyPI..."
	pip install requests packaging
	python check_versions.py

freeze-deps:
	@echo "❄️  Generating current dependency versions..."
	pip freeze > requirements-frozen.txt
	@echo "Current versions saved to requirements-frozen.txt"

# Development targets
test:
	@echo "🧪 Running all tests..."
	pytest

test-fast:
	@echo "⚡ Running fast tests (excluding slow tests)..."
	pytest -m "not slow"

test-performance:
	@echo "⏱  Running performance tests..."
	pytest backend/tests/test_performance.py -v

test-security:
	@echo "🔒 Running security tests..."
	pytest backend/tests/test_security_comprehensive.py -v

test-weasyprint:
	@echo "📄 Running WeasyPrint tests..."
	pytest backend/tests/test_weasyprint_service.py -v

lint:
	@echo "🔍 Running code quality checks..."
	flake8 .
	black --check .
	isort --check-only .
	bandit -r . -x tests/
	safety check

format:
	@echo "✨ Formatting code..."
	black .
	isort .

clean:
	@echo "🧹 Cleaning up temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name "htmlcov" -exec rm -rf {} +

# Environment management
venv:
	@echo "🐍 Creating virtual environment..."
	python3 -m venv venv
	@echo "Activate with: source venv/bin/activate"

requirements:
	@echo "📋 Requirements files:"
	@echo "  requirements.txt           - Core production dependencies"
	@echo "  requirements-test.txt      - Testing and development dependencies"  
	@echo "  requirements-weasyprint.txt - PDF generation dependencies"
	@echo ""
	@echo "Install order:"
	@echo "  1. make install            (core)"
	@echo "  2. make install-weasyprint (PDF generation)"
	@echo "  3. make install-test       (testing)"
	@echo "  OR: make install-dev       (all at once)"
