[tool:pytest]
DJANGO_SETTINGS_MODULE = cvflo.settings
python_files = tests.py test_*.py *_tests.py
python_classes = Test*
python_functions = test_*
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --reuse-db
    --cov=apps
    --cov-report=html
    --cov-report=term-missing
    --cov-fail-under=80
markers =
    unit: Unit tests - fast, isolated tests
    integration: Integration tests - test component interactions
    api: API endpoint tests - test HTTP interfaces
    security: Security tests - authentication, authorization, validation
    performance: Performance tests - load, stress, memory tests
    e2e: End-to-end tests - complete workflow tests
    slow: Slow tests - can be skipped for quick feedback
    pdf: Tests requiring WeasyPrint PDF generation
    supabase: Tests requiring Supabase connectivity
    redis: Tests requiring Redis connectivity
    celery: Tests requiring Celery task processing
testpaths = tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning