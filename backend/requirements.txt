# CVFlo Backend Core Requirements
# Note: No version constraints - let pip resolve dependencies automatically

# Django Core
Django
djangorestframework
django-cors-headers

# Database (SQLite for development, PostgreSQL for production)
psycopg2-binary  # Required for Supabase PostgreSQL

# Celery additional dependencies (core celery/redis/kombu are in requirements-weasyprint.txt)
django-celery-beat

# Authentication & Security
supabase
gotrue
django-ratelimit

# Environment & Configuration
python-decouple
django-environ

# Template Engine
Jinja2

# Logging & Monitoring
django-extensions

# Note: Testing and development dependencies moved to requirements-test.txt
# Note: PDF generation dependencies moved to requirements-weasyprint.txt

# Additional utilities
python-dateutil
requests