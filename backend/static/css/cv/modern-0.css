/**
 * Modern Professional CV Template CSS
 * Clean, modern design with subtle colors and two-column layout
 */

/* Template-specific variables */
:root {
    --primary-color: #2563eb;
    --accent-color: #3b82f6;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #e5e7eb;
    --background-light: #f9fafb;
}

/* Override base styles */
body {
    font-family: 'Calibri', 'Segoe UI', 'Arial', sans-serif;
    color: var(--text-primary);
}

/* Header section */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 2em;
    margin: -20mm -20mm 2em -20mm;
    text-align: center;
}

.header h1 {
    font-size: 28pt;
    font-weight: 300;
    margin-bottom: 0.5em;
    letter-spacing: 0.02em;
}

.header .subtitle {
    font-size: 14pt;
    font-weight: 400;
    opacity: 0.9;
    margin-bottom: 1em;
}

.header .contact-info {
    margin-bottom: 0;
}

.header .contact-item {
    font-size: 10pt;
    opacity: 0.95;
}

/* Two-column layout */
.cv-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3em;
    margin-top: 2em;
}

/* Left sidebar */
.sidebar {
    background: var(--background-light);
    padding: 1.5em;
    margin: 0 -1.5em 0 -20mm;
    padding-left: 20mm;
}

.sidebar .section-title {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    font-size: 12pt;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.sidebar .section {
    margin-bottom: 1.5em;
}

/* Skills in sidebar */
.sidebar .skill-category {
    margin-bottom: 1.2em;
}

.sidebar .skill-category-title {
    color: var(--primary-color);
    font-size: 10pt;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.03em;
    margin-bottom: 0.8em;
}

.sidebar .skill-item {
    padding: 0.3em 0;
    border-bottom: 1px solid var(--border-color);
    font-size: 10pt;
}

.sidebar .skill-item:last-child {
    border-bottom: none;
}

.skill-level-bar {
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    margin-top: 0.3em;
    overflow: hidden;
}

.skill-level-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 2px;
    transition: width 0.3s ease;
}

/* Main content */
.main-content {
    padding-right: 0;
}

.main-content .section-title {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    font-size: 14pt;
    font-weight: 600;
    margin-bottom: 1.2em;
}

/* Experience and education styling */
.experience-item,
.education-item {
    position: relative;
    padding-left: 1.5em;
    margin-bottom: 2em;
    border-left: 3px solid var(--accent-color);
    padding-left: 1.5em;
    margin-left: 0.5em;
}

.experience-item::before,
.education-item::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 0.2em;
    width: 10px;
    height: 10px;
    background: var(--accent-color);
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px var(--accent-color);
}

.item-header {
    margin-bottom: 0.8em;
}

.item-title {
    color: var(--primary-color);
    font-size: 13pt;
    font-weight: 600;
    margin-bottom: 0.2em;
}

.item-organization {
    color: var(--text-secondary);
    font-size: 11pt;
    font-weight: 500;
    font-style: normal;
}

.item-date {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.2em 0.8em;
    border-radius: 12px;
    font-size: 9pt;
    font-weight: 500;
    margin-top: 0.3em;
}

.item-location {
    color: var(--text-secondary);
    font-size: 9pt;
    margin-top: 0.2em;
}

/* Summary section */
.summary-text {
    font-size: 11pt;
    line-height: 1.6;
    color: var(--text-secondary);
    padding: 1em;
    background: var(--background-light);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);
}

/* Projects section */
.project-item {
    background: var(--background-light);
    padding: 1.2em;
    border-radius: 8px;
    margin-bottom: 1.5em;
    border-left: 4px solid var(--accent-color);
}

.project-title {
    color: var(--primary-color);
    font-size: 12pt;
    font-weight: 600;
    margin-bottom: 0.5em;
}

.project-tech {
    font-size: 9pt;
    color: var(--text-secondary);
    margin-bottom: 0.8em;
}

.project-tech .tech-tag {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.2em 0.6em;
    border-radius: 10px;
    margin-right: 0.3em;
    margin-bottom: 0.3em;
    font-size: 8pt;
}

/* Languages section */
.languages-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.8em;
}

.language-item {
    text-align: center;
    padding: 0.8em;
    background: var(--background-light);
    border-radius: 6px;
    border-top: 3px solid var(--primary-color);
}

.language-name {
    font-weight: 600;
    color: var(--primary-color);
    font-size: 10pt;
    margin-bottom: 0.3em;
}

.language-level {
    font-size: 9pt;
    color: var(--text-secondary);
}

/* Print optimizations */
@media print {
    :root {
        --primary-color: #000;
        --accent-color: #333;
        --text-primary: #000;
        --text-secondary: #333;
        --border-color: #ccc;
        --background-light: #f5f5f5;
    }
    
    .header {
        background: #f0f0f0 !important;
        color: #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .sidebar {
        background: #f8f8f8 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .item-date {
        background: #333 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .project-tech .tech-tag {
        background: #333 !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* Simplify complex layouts for print */
    .cv-content {
        grid-template-columns: 0.8fr 2fr;
        gap: 2em;
    }
    
    .sidebar {
        margin-right: 0;
        padding-right: 1em;
    }
    
    /* Ensure proper page breaks */
    .section {
        page-break-inside: avoid;
    }
    
    .experience-item,
    .education-item,
    .project-item {
        page-break-inside: avoid;
        break-inside: avoid;
    }
}

/* Screen-only enhancements */
@media screen {
    .header {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar {
        box-shadow: inset -2px 0 4px rgba(0, 0, 0, 0.05);
    }
    
    .project-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }
}