/**
 * Base CSS for CV templates with print optimization
 * Provides common styles and print media queries for all CV templates
 */

/* Reset and base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Arial', 'Helvetica', 'Liberation Sans', sans-serif;
    font-size: 11pt;
    line-height: 1.5;
    color: #333;
    background: white;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Page setup for print */
@page {
    size: A4;
    margin: 20mm;
    @top-center {
        content: "";
    }
    @bottom-center {
        content: "";
    }
}

/* Container */
.cv-container {
    max-width: 210mm;
    margin: 0 auto;
    padding: 0;
    background: white;
    min-height: 297mm; /* A4 height */
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: 0.5em;
    font-weight: bold;
    page-break-after: avoid;
}

h1 { 
    font-size: 24pt; 
    line-height: 1.2;
}

h2 { 
    font-size: 18pt; 
    line-height: 1.3;
}

h3 { 
    font-size: 14pt; 
    line-height: 1.4;
}

h4 { 
    font-size: 12pt; 
    line-height: 1.4;
}

p, li {
    margin-bottom: 0.5em;
    orphans: 2;
    widows: 2;
}

/* Lists */
ul, ol {
    margin-left: 1.5em;
    margin-bottom: 1em;
}

li {
    padding: 0.1em 0;
}

/* Links */
a {
    color: inherit;
    text-decoration: none;
}

/* Main sections */
.section {
    margin-bottom: 2em;
    page-break-inside: avoid;
}

.section:last-child {
    margin-bottom: 0;
}

.section-title {
    border-bottom: 2px solid #333;
    padding-bottom: 0.2em;
    margin-bottom: 1em;
    font-size: 14pt;
    font-weight: bold;
    page-break-after: avoid;
}

/* Contact information */
.contact-info {
    text-align: center;
    margin-bottom: 2em;
    page-break-inside: avoid;
}

.contact-item {
    display: inline-block;
    margin: 0 0.5em;
}

.contact-item:not(:last-child)::after {
    content: " | ";
    margin-left: 0.5em;
    color: #666;
}

/* Experience and education items */
.experience-item,
.education-item,
.project-item {
    margin-bottom: 1.5em;
    page-break-inside: avoid;
}

.item-header {
    margin-bottom: 0.5em;
}

.item-title {
    font-weight: bold;
    font-size: 12pt;
}

.item-organization {
    color: #666;
    font-style: italic;
    margin-bottom: 0.3em;
}

.item-date {
    color: #666;
    font-size: 10pt;
    margin-bottom: 0.3em;
}

.item-location {
    color: #888;
    font-size: 10pt;
}

.item-description {
    margin-top: 0.5em;
}

.item-description ul {
    margin-top: 0.3em;
    margin-bottom: 0.3em;
}

/* Skills section */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1em;
    margin-bottom: 1em;
}

.skill-category {
    margin-bottom: 1em;
    page-break-inside: avoid;
}

.skill-category-title {
    font-weight: bold;
    margin-bottom: 0.5em;
    font-size: 11pt;
}

.skill-list {
    list-style: none;
    margin-left: 0;
}

.skill-item {
    padding: 0.2em 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.skill-name {
    flex: 1;
}

.skill-level {
    margin-left: 1em;
    font-size: 9pt;
    color: #666;
}

/* Two-column layout support */
.two-column {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2em;
}

.column-left {
    page-break-inside: avoid;
}

.column-right {
    page-break-inside: avoid;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: bold; }
.font-italic { font-style: italic; }

.text-muted { color: #666; }
.text-light { color: #888; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.5em; }
.mb-2 { margin-bottom: 1em; }
.mb-3 { margin-bottom: 1.5em; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.5em; }
.mt-2 { margin-top: 1em; }
.mt-3 { margin-top: 1.5em; }

/* Page break utilities */
.page-break {
    page-break-before: always;
}

.page-break-avoid {
    page-break-inside: avoid;
}

.no-break {
    page-break-inside: avoid;
}

/* Print-specific styles */
@media print {
    body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    .cv-container {
        max-width: none;
        margin: 0;
        padding: 0;
    }
    
    /* Hide interactive elements */
    button, input, select, textarea {
        display: none !important;
    }
    
    /* Optimize typography for print */
    body {
        font-size: 10pt;
        line-height: 1.4;
    }
    
    h1 { font-size: 22pt; }
    h2 { font-size: 16pt; }
    h3 { font-size: 13pt; }
    h4 { font-size: 11pt; }
    
    /* Ensure good page breaks */
    .section {
        page-break-inside: avoid;
    }
    
    .experience-item,
    .education-item,
    .project-item {
        page-break-inside: avoid;
        break-inside: avoid;
    }
    
    /* Reduce margins for print */
    .section {
        margin-bottom: 1.5em;
    }
    
    .experience-item,
    .education-item,
    .project-item {
        margin-bottom: 1em;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    body {
        color: #000;
    }
    
    .text-muted,
    .text-light {
        color: #333;
    }
    
    .section-title {
        border-color: #000;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}