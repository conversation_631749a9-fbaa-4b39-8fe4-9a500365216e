# Docker Compose configuration for WeasyPrint PDF generation service
version: '3.8'

services:
  # Redis for Celery message broker
  redis:
    image: redis:7-alpine
    container_name: cvflo-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL database (if not using Supabase)
  postgres:
    image: postgres:15-alpine
    container_name: cvflo-postgres
    environment:
      POSTGRES_DB: cvflo
      POSTGRES_USER: cvflo
      POSTGRES_PASSWORD: your_password_here
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cvflo"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django application with WeasyPrint
  web:
    build:
      context: .
      dockerfile: Dockerfile.weasyprint
    container_name: cvflo-web
    environment:
      - DEBUG=False
      - DATABASE_URL=***************************************************/cvflo
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - ALLOWED_HOSTS=localhost,127.0.0.1
      - WEASYPRINT_MAX_WORKERS=3
      - WEASYPRINT_TIMEOUT=60
      - WEASYPRINT_MAX_MEMORY_MB=512
      - PDF_EXPIRATION_HOURS=24
    ports:
      - "8000:8000"
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs
      - /tmp:/tmp
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/pdf/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery worker for async PDF generation
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.weasyprint
    container_name: cvflo-celery-worker
    command: celery -A cvflo worker -l info --concurrency=2 --max-tasks-per-child=100
    environment:
      - DEBUG=False
      - DATABASE_URL=***************************************************/cvflo
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - WEASYPRINT_MAX_WORKERS=3
      - WEASYPRINT_TIMEOUT=60
      - WEASYPRINT_MAX_MEMORY_MB=512
    volumes:
      - ./media:/app/media
      - ./logs:/app/logs
      - /tmp:/tmp
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "celery", "-A", "cvflo", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Celery beat for scheduled tasks
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.weasyprint
    container_name: cvflo-celery-beat
    command: celery -A cvflo beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
    environment:
      - DEBUG=False
      - DATABASE_URL=***************************************************/cvflo
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./logs:/app/logs
    depends_on:
      - redis
      - postgres
    restart: unless-stopped

  # Flower for monitoring Celery tasks
  flower:
    build:
      context: .
      dockerfile: Dockerfile.weasyprint
    container_name: cvflo-flower
    command: celery -A cvflo flower --port=5555 --basic_auth=admin:admin123
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
    restart: unless-stopped

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: cvflo-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./media:/app/media:ro
      - ./static:/app/static:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - web
    restart: unless-stopped

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  default:
    name: cvflo-network