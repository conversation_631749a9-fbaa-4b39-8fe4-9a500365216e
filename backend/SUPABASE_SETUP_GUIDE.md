# Django + Supabase Integration Setup Guide

This guide provides step-by-step instructions for setting up the complete Django + Supabase PostgreSQL integration for the CV Builder application.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
cd backend
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Copy the example environment file and update it with your Supabase credentials:

```bash
cp .env.example .env
```

Update the following variables in `.env`:

```bash
# Supabase Database Configuration
DATABASE_ENGINE=supabase
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres.your-project-id
SUPABASE_DB_PASSWORD=your-database-password
SUPABASE_DB_HOST=aws-0-us-east-1.pooler.supabase.com
SUPABASE_DB_PORT=6543
SUPABASE_DB_SSL_MODE=require

# Supabase Authentication
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

### 3. Test Connection

Run the connection test script to verify your setup:

```bash
python scripts/test_supabase_connection.py
```

### 4. Run Migrations

Create and apply database migrations:

```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. Start Development Server

```bash
python manage.py runserver
```

## 📋 Detailed Setup Instructions

### Step 1: Supabase Project Setup

1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Choose a strong database password
   - Wait for the project to be ready

2. **Get Database Credentials**
   - Go to Settings → Database
   - Copy the connection details:
     - Host (use the pooler URL: `aws-0-us-east-1.pooler.supabase.com`)
     - Database name (usually `postgres`)
     - Username (format: `postgres.project-id`)
     - Password (the one you set during project creation)
     - Port (use `6543` for connection pooler)

3. **Get API Keys**
   - Go to Settings → API
   - Copy:
     - Project URL
     - `anon` key (public key)
     - `service_role` key (private key)

### Step 2: Django Configuration

1. **Update Environment Variables**
   ```bash
   # Required for database connection
   DATABASE_ENGINE=supabase
   SUPABASE_DB_NAME=postgres
   SUPABASE_DB_USER=postgres.xzexcljgkqwfqpenjlca
   SUPABASE_DB_PASSWORD=your-actual-password
   SUPABASE_DB_HOST=aws-0-us-east-1.pooler.supabase.com
   SUPABASE_DB_PORT=6543
   SUPABASE_DB_SSL_MODE=require

   # Required for authentication
   SUPABASE_URL=https://xzexcljgkqwfqpenjlca.supabase.co
   SUPABASE_ANON_KEY=your-actual-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key
   ```

2. **Production Security Settings**
   ```bash
   # For production only
   DEBUG=False
   SECRET_KEY=your-secure-secret-key
   ALLOWED_HOSTS=your-domain.com,api.your-domain.com
   ```

### Step 3: Database Schema Setup

1. **Create Tables**
   ```bash
   python manage.py makemigrations cv_builder
   python manage.py migrate
   ```

2. **Verify Tables in Supabase**
   - Go to Supabase Dashboard → Table editor
   - You should see:
     - `cv_data`
     - `pdf_generations`
     - `user_profiles`
     - `rate_limit_logs`

### Step 4: Authentication Setup

1. **Configure Supabase Auth**
   - Go to Supabase Dashboard → Authentication → Settings
   - Set up your authentication providers (email, Google, etc.)
   - Configure email templates if using email auth

2. **Test Authentication**
   ```bash
   # Run the authentication tests
   python manage.py test apps.authentication.tests
   ```

### Step 5: Migration from SQLite (Optional)

If you have existing data in SQLite:

```bash
# Backup your current data
cp db.sqlite3 db.sqlite3.backup

# Run migration command
python manage.py migrate_to_supabase --dry-run  # Test first
python manage.py migrate_to_supabase            # Actual migration
```

## 🔧 Configuration Options

### Database Connection Pooling

```bash
# Connection pooling settings
DB_CONN_MAX_AGE=600          # 10 minutes
DB_MAX_CONNECTIONS=20        # Max connections
DB_MIN_CONNECTIONS=5         # Min connections
```

### Security Settings

```bash
# Rate limiting
RATE_LIMIT_ENABLE=True
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Failed login protection
SUPABASE_MAX_FAILED_ATTEMPTS=5
SUPABASE_LOCKOUT_DURATION=900  # 15 minutes
```

### PDF Generation

```bash
# PDF generation settings
PDF_GENERATION_TIMEOUT=30
PDF_CACHE_TTL=3600
BACKGROUND_PDF_ENABLED=False

# WeasyPrint settings
WEASYPRINT_DPI=300
WEASYPRINT_OPTIMIZE_IMAGES=True
```

## 🧪 Testing

### Run All Tests

```bash
# Run all tests
python manage.py test

# Run specific test suites
python manage.py test apps.authentication
python manage.py test apps.cv_builder
python manage.py test tests.test_supabase_connectivity
```

### Connection Testing

```bash
# Test database connection
python scripts/test_supabase_connection.py

# Test with dry run
python scripts/test_supabase_connection.py --dry-run
```

### Manual API Testing

```bash
# Test authentication endpoint
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Test CV endpoints (with JWT token)
curl -X GET http://localhost:8000/api/cv/profiles/ \
  -H "Authorization: Bearer your-jwt-token"
```

## 🚨 Troubleshooting

### Common Issues

1. **Connection Refused**
   ```
   Error: connection to server at "..." failed: Connection refused
   ```
   - Check if Supabase project is active
   - Verify host, port, and credentials
   - Check firewall settings

2. **SSL Connection Error**
   ```
   Error: SSL connection has been closed unexpectedly
   ```
   - Ensure `SUPABASE_DB_SSL_MODE=require`
   - Check if using correct pooler port (6543)

3. **Authentication Failed**
   ```
   Error: authentication failed for user "postgres.xxx"
   ```
   - Verify database password is correct
   - Check username format: `postgres.project-id`

4. **Permission Denied**
   ```
   Error: permission denied for table "cv_data"
   ```
   - Ensure RLS policies are correctly configured
   - Check if using service role key when needed

### Debug Mode

```bash
# Enable debug logging
LOG_LEVEL=DEBUG
python manage.py runserver

# Check logs
tail -f logs/cvflo.log
```

### Connection Validation

```bash
# Validate production configuration
python manage.py shell
>>> from apps.core.security import connection_validator
>>> connection_validator.validate_production_config()
>>> results = connection_validator.test_connection_security()
>>> print(results)
```

## 📊 Monitoring and Logging

### Log Files

- Application logs: `logs/cvflo.log`
- Error logs: `logs/error.log`
- Audit logs: Check Django logs with `cvflo.audit` logger

### Performance Monitoring

```python
# Monitor database connections
from django.db import connection
print(f"Connection queries: {len(connection.queries)}")

# Check cache performance
from django.core.cache import cache
cache.get_stats()  # If using Redis
```

### Security Monitoring

```bash
# Check for suspicious activities
grep "suspicious_activity" logs/cvflo.log

# Monitor rate limiting
grep "rate_limit" logs/cvflo.log

# Check authentication failures
grep "authentication.*failed" logs/cvflo.log
```

## 🔒 Production Deployment

### Environment Variables

```bash
# Production settings
DEBUG=False
SECRET_KEY=your-production-secret-key
ALLOWED_HOSTS=your-domain.com

# Database settings
DATABASE_ENGINE=supabase
SUPABASE_DB_SSL_MODE=require

# Security settings
SECURE_SSL_REDIRECT=True
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
```

### Security Checklist

- [ ] All Supabase credentials configured
- [ ] SSL enabled (`sslmode=require`)
- [ ] Connection pooling configured
- [ ] Rate limiting enabled
- [ ] Audit logging configured
- [ ] Security middleware enabled
- [ ] Django security settings configured
- [ ] Environment variables secured (not in version control)

### Deployment Commands

```bash
# Install dependencies
pip install -r requirements.txt

# Collect static files
python manage.py collectstatic --noinput

# Run migrations
python manage.py migrate

# Validate deployment
python manage.py check --deploy

# Start production server
gunicorn cvflo.wsgi:application --bind 0.0.0.0:8000
```

## 📚 API Documentation

### Authentication

All API endpoints require Supabase JWT authentication:

```bash
Authorization: Bearer <supabase-jwt-token>
```

### Endpoints

- `GET /api/cv/profiles/` - List user's CV profiles
- `POST /api/cv/profiles/` - Create new CV profile
- `GET /api/cv/profiles/{id}/` - Get specific CV profile
- `PUT /api/cv/profiles/{id}/` - Update CV profile
- `DELETE /api/cv/profiles/{id}/` - Delete CV profile
- `POST /api/pdf/generate/` - Generate PDF from CV

### Error Responses

```json
{
  "error": "Authentication failed",
  "detail": "Invalid or expired token"
}
```

## 🤝 Support

For issues or questions:

1. Check the troubleshooting section above
2. Review Django and Supabase documentation
3. Check application logs for detailed error messages
4. Verify your Supabase project status and configuration

## 📈 Performance Tips

1. **Use Connection Pooling**: Always use the Supabase connection pooler (port 6543)
2. **Enable Caching**: Use Redis for production caching
3. **Optimize Queries**: Use `select_related()` and `prefetch_related()` for complex queries
4. **Monitor Database**: Check query performance in Supabase dashboard
5. **Rate Limiting**: Implement appropriate rate limits for your use case

## 🔄 Updates and Maintenance

### Updating Dependencies

```bash
pip install -r requirements.txt --upgrade
python manage.py migrate
```

### Database Maintenance

```bash
# Check database health
python scripts/test_supabase_connection.py

# Backup database (via Supabase dashboard)
# Monitor connection pool usage
# Review and rotate access keys periodically
```