# Django Configuration
SECRET_KEY=your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
# Options: 'sqlite' (development), 'postgresql' (local), 'supabase' (production)
DATABASE_ENGINE=supabase

# Supabase PostgreSQL Database Settings
SUPABASE_DB_NAME=postgres
SUPABASE_DB_USER=postgres.your-project-id
SUPABASE_DB_PASSWORD=your-database-password
SUPABASE_DB_HOST=aws-0-us-east-1.pooler.supabase.com
SUPABASE_DB_PORT=6543
SUPABASE_DB_SSL_MODE=require

# Database Connection Pooling
DB_CONN_MAX_AGE=600
DB_MAX_CONNECTIONS=20
DB_MIN_CONNECTIONS=5

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
SUPABASE_JWT_SECRET=your-jwt-secret-if-available

# Supabase Storage (for PDF files)
SUPABASE_STORAGE_BUCKET=cv-pdfs
SUPABASE_STORAGE_URL=https://your-project.supabase.co/storage/v1

# PDF Generation Settings
PDF_MAX_FILE_SIZE=10485760  # 10MB in bytes
PDF_GENERATION_TIMEOUT=30
PDF_CACHE_TTL=3600
PDF_GENERATION_POOL_SIZE=3
BACKGROUND_PDF_ENABLED=False
CLEANUP_INTERVAL=24
MAX_CONCURRENT_TASKS=5

# WeasyPrint Configuration
WEASYPRINT_DPI=300
WEASYPRINT_OPTIMIZE_IMAGES=True

# Caching (Redis for production)
# REDIS_URL=redis://localhost:6379/0
PDF_CACHE_TIMEOUT=3600

# CORS Settings (Frontend URLs)
FRONTEND_URL=http://localhost:3000
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173,http://localhost:8000,http://127.0.0.1:8000

# Rate Limiting
RATE_LIMIT_ENABLE=True
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# Logging
LOG_LEVEL=INFO

# Email Configuration (if using Django's email backend)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Storage Configuration (for production)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=us-east-1