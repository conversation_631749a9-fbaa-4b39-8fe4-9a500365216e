<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirm Your CVFlo Account</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #374151;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 40px 30px 30px;
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: white;
        }
        .header h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }
        .header p {
            margin: 8px 0 0;
            font-size: 16px;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .content h2 {
            color: #1F2937;
            font-size: 24px;
            margin: 0 0 20px;
            font-weight: 600;
        }
        .content p {
            margin: 0 0 20px;
            font-size: 16px;
            line-height: 1.7;
        }
        .button {
            display: inline-block;
            background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
            color: white;
            text-decoration: none;
            padding: 16px 32px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-1px);
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        .footer {
            margin-top: 40px;
            padding: 30px;
            background: #F9FAFB;
            border-radius: 8px;
            text-align: center;
        }
        .footer p {
            font-size: 14px;
            color: #6B7280;
            margin: 8px 0;
            line-height: 1.5;
        }
        .footer a {
            color: #3B82F6;
            text-decoration: none;
        }
        .divider {
            height: 1px;
            background: linear-gradient(to right, transparent, #E5E7EB, transparent);
            margin: 30px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>CVFlo</h1>
            <p>Professional CV Builder by JaydeeTech Ltd</p>
        </div>
        
        <div class="content">
            <h2>Welcome to CVFlo! 🎉</h2>
            
            <p>Thank you for signing up with CVFlo. We're excited to help you create professional CVs that stand out.</p>
            
            <p>To get started, please confirm your email address by clicking the button below:</p>
            
            <div class="button-container">
                <a href="{{ .ConfirmationURL }}" class="button">Confirm Your Account</a>
            </div>
            
            <div class="divider"></div>
            
            <p><strong>What you can do with CVFlo:</strong></p>
            <ul style="padding-left: 20px; color: #4B5563;">
                <li>Create professional CVs with multiple templates</li>
                <li>Real-time preview as you build</li>
                <li>Download high-quality PDFs</li>
                <li>Manage multiple CV versions</li>
            </ul>
            
            <p>If you didn't create an account with CVFlo, you can safely ignore this email.</p>
            
            <p style="font-size: 14px; color: #6B7280;">
                <strong>Having trouble?</strong> If the button above doesn't work, copy and paste this link into your browser:<br>
                <a href="{{ .ConfirmationURL }}" style="color: #3B82F6; word-break: break-all;">{{ .ConfirmationURL }}</a>
            </p>
        </div>
        
        <div class="footer">
            <p><strong>CVFlo Team</strong></p>
            <p>This email was sent by CVFlo, a product of JaydeeTech Ltd.</p>
            <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p style="margin-top: 20px; font-size: 12px;">
                This confirmation link will expire in 24 hours for security reasons.
            </p>
        </div>
    </div>
</body>
</html>