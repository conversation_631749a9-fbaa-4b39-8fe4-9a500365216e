{"name": "cvflo-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "bun run --bun vite", "build": "bun run --bun vite build", "lint": "bun run --bun eslint .", "preview": "bun run --bun vite preview"}, "dependencies": {"@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.52.1", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tinymce/tinymce-react": "^6.2.1", "lucide-react": "^0.522.0", "pdf-lib": "^1.17.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "tinymce": "^7.9.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/cheerio": "^1.0.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/supertest": "^6.0.3", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "cheerio": "^1.1.0", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "supertest": "^7.1.1", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0", "vite": "^6.3.5"}}